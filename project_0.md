

创建虚拟环境的步骤：

第一步：
(base) zhengjunwang@Zhengjuns-MacBook-Pro VQA_projects % cd project_0 

第二步：
(base) zhengjunwang@Zhengjuns-MacBook-Pro project_0 % python -m venv venvproj_0

第三步：
(base) zhen<PERSON><PERSON>wang@Zhengjuns-MacBook-Pro project_0 % cd venvproj_0 

第四步：
(base) zhengjunwang@Zhengjuns-MacBook-Pro venvproj_0 % cd bin


第五步：
(base) zhengjunwang@Zhengjuns-MacBook-Pro bin % source activate 


继续运行步骤：

第六步：
(venvproj_0) (base) zhengjunwang@Zhengjuns-MacBook-Pro bin % cd ..  

第七步：
(venvproj_0) (base) zhengjunwang@Zhengjuns-MacBook-Pro venvproj_0 % cd ..

第八步：
(venvproj_0) (base) zhengjunwang@Zhengjuns-MacBook-Pro project_0 % cd main_proj_0 

第九步：
(venvproj_0) (base) zhengjunwang@Zhengjuns-MacBook-Pro main_proj_0 % python draw_XYZ_optimizator_v1.py


安装一堆包：

(venvproj_0) (base) zhengjunwang@Zhengjuns-MacBook-Pro project_0 % pip install -r requirements.txt 



## 使用 Homebrew 安装 OpenMPI 并通过 pip 安装 mpi4py (这是虚拟环境venvproj_0的使用)

1. 安装 Homebrew（如果尚未安装）
Homebrew 是 macOS 上的包管理工具，可以通过以下命令安装：

Copy code
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

2. 使用 Homebrew 安装 OpenMPI
安装 OpenMPI：

brew install open-mpi
3. 确保 Homebrew 的 OpenMPI 在 PATH 中

安装完成后，确保 Homebrew 的 OpenMPI 编译器位于 PATH 的优先位置。可以通过以下命令检查：

Copy code
which mpicc
输出应指向 /opt/homebrew/bin/mpicc 或类似路径，而不是 Anaconda 的路径。如果不正确，可以在终端配置文件（如 .bash_profile 或 .zshrc）中调整 PATH。例如，添加以下行以优先使用 Homebrew 的 OpenMPI：

Copy code
export PATH="/opt/homebrew/bin:$PATH"
然后，重新加载配置文件：

Copy code
source ~/.zshrc  # 或者 source ~/.bash_profile
4. 激活您的虚拟环境
确保您的虚拟环境已激活：
bash
Copy code
source venvproj_0/bin/activate

5. 使用 pip 安装 mpi4py
安装 mpi4py：
Copy code
pip install mpi4py

6. 验证安装

安装完成后，验证 mpi4py 是否正确安装：
Copy code
python -c "import mpi4py; print(mpi4py.__version__)"
如果没有错误并且显示版本号，说明安装成功。



使用 Homebrew 安装 OpenMPI 并通过 pip 安装 mpi4py
如果您更倾向于使用 pip，可以按照以下步骤操作：

安装 Homebrew（如果尚未安装）

Homebrew 是 macOS 上的包管理工具，可以通过以下命令安装：

bash
Copy code
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
使用 Homebrew 安装 OpenMPI

安装 OpenMPI：

bash
Copy code
brew install open-mpi
确保 Homebrew 的 OpenMPI 在 PATH 中

安装完成后，确保 Homebrew 的 OpenMPI 编译器位于 PATH 的优先位置。可以通过以下命令检查：

bash
Copy code
which mpicc
输出应指向 /opt/homebrew/bin/mpicc 或类似路径，而不是 Anaconda 的路径。如果不正确，可以在终端配置文件（如 .bash_profile 或 .zshrc）中调整 PATH。例如，添加以下行以优先使用 Homebrew 的 OpenMPI：

bash
Copy code
export PATH="/opt/homebrew/bin:$PATH"
然后，重新加载配置文件：

bash
Copy code
source ~/.zshrc  # 或者 source ~/.bash_profile
激活您的虚拟环境

确保您的虚拟环境已激活：

bash
Copy code
source venvproj_0/bin/activate
使用 pip 安装 mpi4py

安装 mpi4py：

bash
Copy code
pip install mpi4py
验证安装

安装完成后，验证 mpi4py 是否正确安装：

bash
Copy code
python -c "import mpi4py; print(mpi4py.__version__)"
如果没有错误并且显示版本号，说明安装成功。




## 如何列出所有 Conda 环境？
conda env list
使用  0_proj_conda 环境
(base) zhengjunwang@Zhengjuns-MacBook-Pro VQA_projects % conda activate 0_proj_conda