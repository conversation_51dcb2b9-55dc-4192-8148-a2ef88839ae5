导出当前 Conda 环境到 environment.yml 文件：

bash
Copy code
conda env export > environment.yml
在另一台机器或新环境中通过 environment.yml 创建环境：

bash
Copy code
conda env create -f environment.yml
激活新创建的环境：

bash
Copy code
conda activate your_env_name





# 量子压缩感知项目--oscar 给我们的项目环境，从他的项目延伸出来的。

虚拟环境是用的这个：0_proj_conda   是conda 的  ，对应的python 版本是  Python 3.10.16



# 总结如何运行这个项目project_0：20250218

第一步：新开的终端需要退出base环境，命令如下:
(base) dhcp-172-21-93-150:VQA_projects zhengjunwang$ conda deactivate

第二步： 激活conda的虚拟环境，
(base) dhcp-172-21-93-150:VQA_projects zhengjunwang$ conda activate 0_proj_conda

第三步：进入到project_0项目下，具体的命令如下：
(0_proj_conda) dhcp-172-21-93-150:VQA_projects zhengjunwang$ cd /Users/<USER>/Desktop/VQA_projects/project_0/main_proj_0

第四步：直接运行主函数代码：draw_XYZ_optimizator_v1.py






# 20250219 项目的0的问题总结
之前我们做2个量子比特的时候，我们分两个块，主函数（draw_XYZ_optimizator_successed.py）和子函数（core_ncs_code_v1.py）。
主函数主要是是vqa框架在x,y,z方向的测量，并且它里边包含了它自己的2量子比特创建，但是我们发现没有调用子函数的2个量子比特的创建，也就是它只有了1次4维度张量的压缩感知，这个有问题，我们需要调整这里。
对于子函数，它是正确的，并且创建了两个函数，一个四维度张量的压感感知，一个2量比特的稀疏重建，小四维度嵌套到大四维度。并且用了2次4d压缩感知，正确的，只是没有在用到vqa框架测量的x,y,z方向上的密度矩阵的实现。

因此，方案有两种，一种是基于子函数模块修改，只需要子函数跑出来的结构在vqa框架上显示。一种是在主函数上修改2量子比特的结构，嵌套调用2次子函数中的4维度张量的压缩感知即可。最后把他们做成n维度的最好。