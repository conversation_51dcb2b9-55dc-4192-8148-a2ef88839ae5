import numpy as np
import measurement.ancilla as anc

from scipy import linalg
from qiskit import Aer
from optimization.optimizers import ADAM
from IO import get_logger

simulator = Aer.get_backend("aer_simulator")
logger = get_logger(__name__)


def construct_unitary_by_params(params):
    x_0, x_1, x_2, x_3, x_4, x_5, x_6, x_7 = params
    u_0 = np.asarray(
        [
            np.cos(x_0 * 2 * np.pi) * np.sin(x_1 * np.pi) * np.sin(x_2 * np.pi),
            np.sin(x_0 * 2 * np.pi) * np.sin(x_1 * np.pi) * np.sin(x_2 * np.pi),
            np.cos(x_1 * np.pi) * np.sin(x_2 * np.pi),
            np.cos(x_2 * np.pi),
        ]
    ).reshape((-1, 1))
    # with np.printoptions(precision=3, suppress=True):
    #     print(f"u_0 = \n{u_0}")
    #     print(f"Norm of u_0: {linalg.norm(u_0)}\n")
    r = np.asarray(
        [
            np.cos(x_3 * 2 * np.pi)
            * np.sin(x_4 * np.pi)
            * np.sin(x_5 * np.pi)
            * np.sin(x_6 * np.pi)
            * np.sin(x_7 * np.pi),
            np.sin(x_3 * 2 * np.pi)
            * np.sin(x_4 * np.pi)
            * np.sin(x_5 * np.pi)
            * np.sin(x_6 * np.pi)
            * np.sin(x_7 * np.pi),
            np.cos(x_4 * np.pi)
            * np.sin(x_5 * np.pi)
            * np.sin(x_6 * np.pi)
            * np.sin(x_7 * np.pi),
            np.cos(x_5 * np.pi) * np.sin(x_6 * np.pi) * np.sin(x_7 * np.pi),
            np.cos(x_6 * np.pi) * np.sin(x_7 * np.pi),
            np.cos(x_7 * np.pi),
        ]
    ).reshape((-1, 1))

    z = r[::2] + 1j * r[1::2]
    null_mat = linalg.null_space(np.conjugate(u_0).T)
    u_1 = (
        z[0] * null_mat[:, 0] + z[1] * null_mat[:, 1] + z[2] * null_mat[:, 2]
    ).reshape((-1, 1))
    # with np.printoptions(precision=3, suppress=True):
    #     print(f"u_1 = \n{u_1}")
    #     print(f"Norm of u_1: {linalg.norm(u_1)}")
    #     print(
    #         "Scalar product of u_0 and u_1: ",
    #         np.dot(np.conjugate(u_0).reshape(1, -1), u_1),
    #     )

    unitary_rel = np.hstack((u_0, u_1))
    null_mat = linalg.null_space(np.conjugate(unitary_rel).T)

    u_2 = (null_mat[:, 0] / np.linalg.norm(null_mat[:, 0])).reshape((-1, 1))
    u_3 = (null_mat[:, 1] / np.linalg.norm(null_mat[:, 1])).reshape((-1, 1))
    unitary_tot = np.hstack((u_0, u_1, u_2, u_3))
    if np.allclose(np.dot(np.conjugate(unitary_tot).T, unitary_tot), np.eye(4)):
        pass
    else:
        raise ValueError("Input matrix is not unitary")

    return unitary_tot


def construct_povm_effects(unitary):
    effects_lst = []
    for i in range(4):
        PI_i = np.outer(np.conjugate(unitary[i, :2]), unitary[i, :2])
        effects_lst.append(PI_i)

    effects_arr = np.asarray(effects_lst, dtype=np.complex128)
    if np.allclose(np.sum(effects_arr, axis=0), np.eye(2)):
        pass
    else:
        print("ERROR: Effects do not fulfill summing up to identity!")

    effects_arr = effects_arr.reshape((4, 1, -1))
    a_mat = np.vstack([effects_arr[:, 0, i] for i in range(4)])
    b = [1, 0, 0, -1]
    omega_arr = np.linalg.solve(a_mat, b)

    # Check decomposition of sigma_z into effects and omega_arr
    effects_arr = effects_arr.reshape((4, 2, 2))
    _check = np.zeros((2, 2), dtype=np.complex128)
    for i in range(4):
        _check += omega_arr[i] * effects_arr[i, :, :]

    sigma_z = np.asarray([[1, 0], [0, -1]])
    if np.allclose(_check, sigma_z):
        pass
    else:
        raise ValueError("Sigma_z decomposition is corrupted")

    return effects_arr, omega_arr


def decompose_shifted_effects(measured_effects_arr, shifted_effects_arr):
    d_mat = np.zeros((4, 4), dtype=np.complex128)
    for i in range(4):
        measured_effects_arr = measured_effects_arr.reshape((4, 1, -1))
        # print(measured_effects_arr[i])
        # print(shifted_effects_arr[i])
        a_mat = np.vstack([measured_effects_arr[:, 0, i] for i in range(4)])
        b = shifted_effects_arr[i].reshape(4)
        d_arr = np.linalg.solve(a_mat, b)
        # print(d_arr)
        d_mat[i, :] = d_arr

        measured_effects_arr = measured_effects_arr.reshape((4, 2, 2))
        _check = np.zeros((2, 2), dtype=np.complex128)
        for j in range(4):
            _check += d_arr[j] * measured_effects_arr[j, :, :]

    return d_mat


def measure_wrapper(params):
    unitary = construct_unitary_by_params(params)
    effects, omega_arr = construct_povm_effects(unitary)

    def measure(nw, shots):
        nonlocal params
        nonlocal unitary
        nonlocal effects
        nonlocal omega_arr
        if shots != 0:
            sigma, var, prob_arr = anc.measure_sigma_by_povm(
                unitary, omega_arr, shots, qc=nw.qc_assigned
            )
            # sigma2 = anc.measure_sigma_by_density_matrix(nw.qc_assigned, nw.type, nw.IPs[0])
            # logger.info(f"Povm compared to exact, {sigma}, exact: {sigma2}")
        else:
            sigma = anc.measure_sigma_exact_copy(nw.qc_assigned)
        return sigma

    return measure


class CostManagerPOVM:
    def __init__(self, qc, shots):
        self.var_arr = []
        self.sigma_arr = []
        self.qc = qc
        self.shots = shots

    def cost_fun(self, params):
        # For POVM optimization
        for param in params:
            if param == 0:
                param += 1e-5
            if param == 1:
                param -= 1e-5

        unitary = construct_unitary_by_params(params)
        effects, omega_arr = construct_povm_effects(unitary)
        sigma, var, prob_arr = anc.measure_sigma_by_povm(
            unitary, omega_arr, shots=self.shots, qc=self.qc
        )
        return sigma, var

    def cost_and_gradient_fun(self, params):
        # For POVM optimization, var and gradient
        num_params = len(params)
        for param in params:
            if param == 0:
                param += 1e-5
            if param == 1:
                param -= 1e-5

        unitary = construct_unitary_by_params(params)
        effects, omega_arr = construct_povm_effects(unitary)
        sigma, var, prob_arr = anc.measure_sigma_by_povm(
            unitary, omega_arr, shots=self.shots, qc=self.qc
        )

        h = 1e-3
        gradient = np.zeros(num_params)
        params_shifted_mat = np.vstack([params] * num_params)
        params_shifted_mat += np.eye(num_params) * h
        for k in range(num_params):
            params_shifted = params_shifted_mat[k]
            unitary_shifted = construct_unitary_by_params(params_shifted)
            effects_shifted_arr, omega_arr_shifted = construct_povm_effects(
                unitary_shifted
            )
            d_mat = decompose_shifted_effects(effects, effects_shifted_arr)
            second_moment = np.real(np.sum((omega_arr**2) * prob_arr))
            second_moment_shifted = np.real(
                np.sum(
                    np.sum(np.multiply(d_mat.T, (omega_arr_shifted**2)), axis=1)
                    * prob_arr
                )
            )
            d_xk = (second_moment_shifted - second_moment) / h
            gradient[k] = d_xk
        self.var_arr.append(var)
        self.sigma_arr.append(sigma)
        return sigma, var, gradient


def create_optimized_povm_measure(qc, shots, return_costmanager=False):
    # params = np.zeros(8)+1e-3
    params = np.random.random(8)
    costmanager = CostManagerPOVM(qc, shots=shots)
    logger.info(
        "##################### STARTING OPTIMIZATION OF POVM  ############################"
    )
    if shots != 0:
        adam = ADAM(IT=500, LR=0.001, BETA1=0.9, BETA2=0.999)
        new_params = adam.minimize(costmanager, params, iter_save=0)
    else:
        new_params = params

    logger.info(
        "################### Finished POVM optimization #######################"
    )
    measure = measure_wrapper(new_params)
    if return_costmanager:
        return measure, costmanager
    else:
        return measure
