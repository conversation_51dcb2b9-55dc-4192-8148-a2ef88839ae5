import time
import copy
import numpy as np
import os

os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"
import tensorflow as tf

if "OMP_NUM_THREADS" in os.environ:
    omp_num_threads = int(os.getenv("OMP_NUM_THREADS"))
    tf.config.threading.set_inter_op_parallelism_threads(omp_num_threads)
    tf.config.threading.set_intra_op_parallelism_threads(omp_num_threads)

import tensorflow_probability as tfp

from mpi4py import MPI
from IO import get_logger
from qiskit.algorithms.optimizers import SPSA
from vqa.optimization.termination import TerminationChecker, TerminationCheckerBFGS
from scipy.optimize import minimize
from optimization.schedulers import SlopeBasedDecay

tf.autograph.set_verbosity(1)
logger = get_logger(__name__)

comm = MPI.COMM_WORLD
rank = comm.Get_rank()


class Optimizer:
    def __init__(self, name, initial_learning_rate: float = 0.0, adapt="NO", **kwargs):
        self.name = name

        # Adapt hyperparameters
        self.initial_lr = copy.copy(initial_learning_rate)
        self.scheduler = SlopeBasedDecay(
            self, initial_learning_rate=initial_learning_rate, adapt=adapt, **kwargs
        )
        self.natgrad = False
        if "NATGRAD" in kwargs.keys():
            if kwargs["NATGRAD"] == "YES":
                self.natgrad = True

        # Saving information
        self.cost_lst = []
        self.dtheta_arr = []

    def transform_gradient(self, params, gradient):
        if rank == 0:
            if self.natgrad:
                fsmt = self.nw_ansatz.construct_blockdiag_fubini_study_metric_tensor(params)
                try:
                    fsmt_inv = np.linalg.inv(fsmt)
                except:
                    logger.info("Fubiny-Study Metric Tensor is singular!!")
                    fsmt += np.diag(np.ones(fsmt.shape[0]) * 1e-4)
                    fsmt_inv = np.linalg.inv(fsmt)
                logger.info("TRANSFORMING GRADIENT")
                return fsmt_inv @ gradient
            else:
                return gradient
        else:
            return gradient

    def get_cost_data(
        self, costmanager, params, t, hdf5handler=None, iter_save=1, order=0
    ):
        gradient = None
        if order == 0:
            cost = costmanager.cost_fun(params)
        elif order == 1:
            cost, gradient = costmanager.cost_and_gradient_fun(params)
            gradient = self.transform_gradient(params, gradient)
        else:
            raise NotImplementedError("Only zero and first order implemented!")

        self.cost_lst.append(cost)
        if iter_save != 0 and (t % iter_save) == 0:
            if rank == 0:
                true_cost = costmanager.true_cost_arr[-1]
                anc_densities = [(nw.type, nw.measure_handler.rho_list[-1])  # Adjust to be only real part of purity
                                 for nw in costmanager.networks if (nw.type in ["K", "V"])]
                hdf5handler.save_iteration(cost, true_cost, gradient, params, anc_densities, t)
                logger.info(f"{self.name}-Iteration: {t}; Cost={cost}")
                logger.info(f"{self.name}-Iteration: {t}; True Cost={true_cost}")

        if order == 0:
            return cost

        elif order == 1:
            return cost, gradient

    def check_termination(self):
        if len(self._cost_lst) > 30:
            rel_diff = (
                abs(self._cost_lst[-29] - self._cost_lst[-1]) / self._cost_lst[-29]
            )
            rel_diff2 = (
                abs(self._cost_lst[-30] - self._cost_lst[-2]) / self._cost_lst[-30]
            )
            # if rank == 0:
            #     logger.info(f"Rel. Diff: {rel_diff}")
            #     logger.info(self._cost_lst[-29])
            #     logger.info(self._cost_lst[-1])
            # # if rel_diff < 0.05 and rel_diff2 < 0.05:
            #     # return True

            # pp = np.polyfit(range(30), self._cost_lst[-30:], 1)
            # slope = pp[0]
            # if abs(slope) < 1e-2:
            #     print(slope)
            #     print(abs(slope))
            #     return True

            rel_diff = (
                abs(self._cost_lst[-29] - self._cost_lst[-1]) / self._cost_lst[-29] / 28
            )
            if rank == 0:
                logger.info(f"Rel. Diff: {rel_diff}")
            if rel_diff < 1e-4:
                return True
        return False

    def minimize(
        self, costmanager, initial_params, hdf5handler=None, iter_save=5, offset=0
    ):
        if self.name in ["ADAM", "ADAGRAD", "SGD", "NADAM", "ADAMAX"]:
            params = self.minimize_first_order(
                costmanager,
                initial_params,
                hdf5handler=hdf5handler,
                iter_save=iter_save,
                offset=offset,
            )
            return params

        elif self.name == "NATGRAD":
            params = self.minimize_custom(costmanager, initial_params, hdf5handler, iter_save=5, offset=0)
            return params

        elif self.name == "BFGS":
            params = self.minimize_custom(costmanager, initial_params, hdf5handler)
            return params

        elif self.name == "SPSA":
            params, cost_lst = self.minimize_custom(costmanager, initial_params)
            return params, cost_lst

    def minimize_zero_order(self, costmanager, initial_params):
        logger.debug(
            f"######### Starting {self.name} Optimization #########\n###### Using {self.it} as max iterations #####"
        )
        ## costmanager needs instance method 'cost_fun'
        result = self.optimizer.minimize(costmanager.cost_fun, initial_params)
        params = result.x
        cost_lst = self.term_checker.cost_lst
        cost_lst.append(result.fun)
        logger.debug(f"Final cost after zero order optimization: {result.fun}")
        return params, cost_lst

    def minimize_first_order(
        self, costmanager, initial_params, hdf5handler=None, iter_save=5, offset=0
    ):
        # costmanager needs instance methods cost_fun and cost_and_gradient_func
        params = initial_params
        if rank == 0:
            tf_params = tf.Variable(params)
            self.optimizer.build([tf_params])

        for t in range(offset, self.it + offset):
            cost, gradient = self.get_cost_data(
                costmanager, params, t, hdf5handler, iter_save, order=1
            )
            if rank == 0:
                gradient = tf.convert_to_tensor(gradient)
                self.optimizer.apply_gradients([(gradient, tf_params)])
                params = tf_params.numpy()

        self.get_cost_data(
            costmanager, params, self.it + offset, hdf5handler, iter_save, order=1
        )
        return params


##################################################################################
##################################################################################
#################        FIRST ORDER OPTIMIZERS           ########################
##################################################################################
##################################################################################


class SPSAOptimizer(Optimizer):
    def __init__(self, IT: int = 5, **kwargs):
        self.it = IT
        self.term_checker = TerminationChecker(100, self.it)
        self.optimizer = SPSA(maxiter=self.it, termination_checker=self.term_checker)
        super().__init__(name="SPSA", **kwargs)

    def minimize_custom(self, costmanager, initial_params):
        result = self.optimizer.minimize(costmanager.cost_fun, initial_params)
        params = result.x
        cost_lst = self.term_checker.cost_lst
        cost_lst.append(result.fun)
        logger.debug(f"Final cost after zero order optimization: {result.fun}")
        return params, cost_lst


##################################################################################
##################################################################################
#################        FIRST ORDER OPTIMIZERS           ########################
##################################################################################
##################################################################################


class BFGS(Optimizer):
    def __init__(
        self, nw_ansatz, adapt: str = "NO", IT=10, gtol: float = 10e-15, **kwargs
    ):
        self.it = IT
        super().__init__("BFGS", adapt=adapt, **kwargs)
        self.tf_minimize = tfp.optimizer.bfgs_minimize

    def minimize_custom(self, costmanager, initial_params, hdf5handler):
        logger.info("MINIMIZING")
        tf_params = tf.Variable(initial_params)
        result = self.tf_minimize(
            costmanager.cost_and_gradient_fun,
            tf_params,
            max_iterations=self.it,
            max_line_search_iterations=10,
            f_relative_tolerance=-10,
            f_absolute_tolerance=-10,
            stopping_condition=BFGS.stopping_condition,
        )
        print(result)
        print(result.num_objective_evaluations.numpy())
        if rank == 0:
            hdf5handler.set_attributes(
                {"Obj. evaluations": result.num_objective_evaluations.numpy()}
            )
        return result.position.numpy()

    @staticmethod
    def stopping_condition(a, b):
        return False

    # def minimize(self, costfunction, initial_params, hdf5handler):
    #     params = initial_params
    #     res = minimize(
    #         costfunction,
    #         params,
    #         method="L-BFGS-B",
    #         jac=True,
    #         callback=self.term_checker,
    #         options={"disp": False, "maxiter": self.it, "gtol": 0, "eps": -10},
    #     )
    #     params = res.x
    #
    #     return params


class ADAM(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        BETA1=0.9,
        BETA2=0.999,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.nw_ansatz = nw_ansatz
        self.m_t, self.v_t = 0, 0
        self.beta1 = BETA1
        self.beta2 = BETA2
        self.epsilon = EPSILON
        self.it = IT
        super().__init__(name="ADAM", initial_learning_rate=LR, adapt=adapt, **kwargs)
        self.optimizer = tf.keras.optimizers.Adam(
            learning_rate=self.scheduler, epsilon=self.epsilon
        )


class NADAM(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        BETA1=0.9,
        BETA2=0.999,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.nw_ansatz = nw_ansatz
        self.it = IT
        self.beta1 = BETA1
        self.beta2 = BETA2
        self.epsilon = EPSILON
        super().__init__(name="NADAM", initial_learning_rate=LR, adapt=adapt, **kwargs)
        self.optimizer = tf.keras.optimizers.Nadam(
            learning_rate=self.scheduler, epsilon=self.epsilon
        )


class ADAGRAD(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        BETA1=0.9,
        BETA2=0.999,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.nw_ansatz = nw_ansatz
        self.m_t, self.v_t = 0, 0
        self.beta1 = BETA1
        self.beta2 = BETA2
        self.epsilon = EPSILON
        self.it = IT
        super().__init__(
            name="ADAGRAD", initial_learning_rate=LR, adapt=adapt, **kwargs
        )
        self.optimizer = tf.keras.optimizers.Adagrad(
            learning_rate=self.scheduler, epsilon=1e-7
        )


class SGD(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        BETA1=0.9,
        BETA2=0.999,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.nw_ansatz = nw_ansatz
        self.m_t, self.v_t = 0, 0
        self.beta1 = BETA1
        self.beta2 = BETA2
        self.epsilon = EPSILON
        print(self.epsilon)
        self.it = IT
        super().__init__(name="SGD", initial_learning_rate=LR, adapt=adapt, **kwargs)
        self.optimizer = tf.keras.optimizers.experimental.SGD(
            learning_rate=self.scheduler
        )


class ADAMAX(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.ansatz = nw_ansatz
        self.it = IT
        self.epsilon = EPSILON
        self.lr = LR
        super().__init__(
            name="ADAMAX", initial_learning_rate=self.lr, adapt=adapt, **kwargs
        )
        self.optimizer = tf.keras.optimizers.Adamax(learning_rate=self.scheduler)


class NatGrad(Optimizer):
    def __init__(self, nw_ansatz, adapt, **kwargs):
        options = {
            "LR": 0.005,
            "IT": 100,
        }
        options.update(kwargs)  # Right now ignoring this
        self.nw_ansatz = nw_ansatz
        self.lr = options["LR"]
        self.it = options["IT"]
        super().__init__("NATGRAD", initial_learning_rate=self.lr, adapt=adapt, **kwargs)

    def do_step(self, params, gradient):
        FSMT = self.nw_ansatz.construct_blockdiag_fubini_study_metric_tensor(params)
        # print(self.nw_ansatz.qc)
        # print(FSMT.shape, gradient.shape)
        # print(FSMT)
        b = -1 * self.lr * gradient
        print(self.lr)
        try:
            d_theta = np.linalg.solve(FSMT, b)
        except:
            logger.info("Fubiny-Study Metric Tensor is singular!!")
            FSMT += np.diag(np.ones(FSMT.shape[0]) * 1e-4)
            d_theta = np.linalg.solve(FSMT, b)
        updated_params = params + d_theta
        self.dtheta_arr.append(np.linalg.norm(d_theta))
        return updated_params

    def minimize_custom(
        self, costmanager, initial_params, hdf5handler=None, iter_save=5, offset=0
    ):
        params = initial_params
        for t in range(offset, self.it + offset):
            cost, gradient = self.get_cost_data(
                costmanager, params, t, hdf5handler, iter_save, order=1
            )
            if rank == 0:
                params = self.do_step(params, gradient)

            # Update learning rate, if adaptive criterion is triggered
            self.lr = self.scheduler()

        self.get_cost_data(
            costmanager, params, self.it + offset, hdf5handler, iter_save, order=1
        )
        return params


def get_optimizer(name):
    if name.upper() == "ADAM":
        return ADAM
    elif name.upper() == "NADAM":
        return NADAM
    elif name.upper() == "ADAGRAD":
        return ADAGRAD
    elif name.upper() == "SGD":
        return SGD
    elif name.upper() == "ADAMAX":
        return ADAMAX
    elif name.upper() == "BFGS":
        logger.info("BFGS supported with SHOTS but should be used carefully!")
        return BFGS
    elif name.upper() == "SPSA":
        return SPSAOptimizer
    elif name.upper() == "NATGRAD":
        return NatGrad
    else:
        raise Exception(f"The optimizer {name} is not supported!")
