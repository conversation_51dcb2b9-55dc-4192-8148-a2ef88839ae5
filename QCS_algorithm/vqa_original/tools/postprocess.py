import numpy as np
import itertools


def get_probability_array(counts, shots):
    num_qubits = len(list(counts.keys())[0])
    prob_arr = np.zeros(2**num_qubits)
    for j, binary_tuple in enumerate(itertools.product("01", repeat=num_qubits)):
        binary_str = "".join(binary_tuple)
        if binary_str in counts.keys():
            prob_arr[j] = counts[binary_str] / shots

    return prob_arr


def get_povm_variance(omega_arr, prob_arr, shots):
    omega_sq_exp = np.real(np.sum((omega_arr**2) * prob_arr))
    omega_exp_sq = np.real(np.sum(omega_arr * prob_arr)) ** 2
    var = np.sqrt(omega_sq_exp - omega_exp_sq) / shots

    return var
