# import uuid    # 生成唯一id 工具包
# from copy import copy   深浅拷贝
import copy

import numpy as np   #矩阵操作
from qiskit import QuantumCircuit, QuantumRegister    #量子计算得开源得sdk ， QuantumCircuit导入了量子电路，  QuantumRegister 量子寄存器得相关类

# from qiskit.circuit.library import EfficientSU2
from qiskit import Aer   #获取量子模拟器，， 模拟量子电路后端

simulator = Aer.get_backend("aer_simulator")
from qiskit.circuit import ParameterVector   #用于量子电路中定义可参数化得向量


class SU2:   #量子模拟 和生成量子电路模板
    """
    量子模拟得模板电路
    """
    # #初始化类  SU2 的示例， 设置ansatz 的各类参数
    def __init__(
        self,
        ip_number,  # 输入量子比特的数量
        reps,      #量子门的重复次数
        layer_scheme="YC",    # 用于设置 ansatz的结构 (层的类型) ， 默认为 ”YC"
        include_barriers=True,  # 是否再量子电路中加入障碍 (用于电路优化)， 默认为True
        entanglement="circular",  # 量子比特间纠缠得类型， 默认为circular
    ):    
        ## 属性初始化
        self.registers = [QuantumRegister(ip_number)]  # 创建量子寄存器， 量子比特数为ip_number
        self.ip_number = ip_number   #存量子比特数量
        self.reps = reps   # 存储量子门重复的层数
        self.scheme = layer_scheme   #存储ansatz 得结构方案
        self.include_barriers = include_barriers  # 是否包含障碍， 用于优化
        self.entanglement = entanglement    #纠缠类型
        # 初始化量子电路和参数向量
        self.qc = QuantumCircuit(*self.registers)  # 使用*self.registers 初始化一个量子电路
        self.natgrad_blocks = self.construct_natgrad_blocks_2(ip_number, reps)    #调用construct_natgrad_blocks_2方法构建自然梯度得参数块，并且获取参数数量
        self.num_parameters, self.param_vector = self.setup_ansatz()  # 调用setup_ansatz，来设置ansatz(量子态的参数化形式) 并获取参数数量 和参数向量
        self.num_layers = (len(layer_scheme)-1) * (1 + reps)    # self.num_layers：计算量子电路中的总层数。len(layer_scheme)：表示每个layer_scheme的长度-1. 
                                                 # （1 + reps）：表示它额外重复的次数。
    def setup_ansatz(self):     # 定义了一个用于设置ansatz的方法
        # num_parameters：计算电路中需要的参数总数。len(self.scheme) - 1：计算除去某一部分后的层数。self.ip_number * (self.reps + 1) ：乘以量子比特数量和重复次数
        num_parameters = (len(self.scheme) - 1) * self.ip_number * (self.reps + 1)    
        # layers_str：生成量子层的字符串序列。 [:-1]：去掉最后一个字符
        layers_str = (self.scheme * (self.reps + 1))[:-1]    #移除最前面和最后一项，确保正确的层数。 self.scheme= = 'RCRC'
        param_vector = ParameterVector("SU2", num_parameters)  # 用于定义参数向量，用于后续层。param_vector：创建一个参数向量对象，名字为："SU2"，用于量子电路中的可调参数
        count = 0   #  初始化寄存器，用于追踪参数索引
        for i, layer in enumerate(layers_str):   # 遍历每一个层的字符串，遍历layers_str中的每一个字符以及它的索引
            if layer == "C":     # 如果当前层是CNOT门，则添加CNOT门
                SU2.append_cnot_layer(self.qc, self.ip_number, self.entanglement)
                if self.include_barriers:   # 如果设置了include_barriers（障碍）
                    self.qc.barrier()       # 向量子电路中添加障碍 barrier，用于隔离不同量子门的影响，增加清晰性
            else:                       # 如果当前层不是C则分配一部分参数给这一层。
                params = param_vector[    # 这一部分从param_vector中选择参数，
                    self.ip_number * count : self.ip_number * (count + 1)  # 
                ]
                SU2.append_r_layer(self.qc, params, layer)     # 调用append_r_layer方法，添加旋转层到量子电路
                count += 1         # 更新计数器，确保下一次循环时索引正确
        return num_parameters, param_vector     # 返回计算得到的参数数量和参数向量。

    def construct_natgrad_blocks(self, ip_number, reps):
        layers_str = ("YC" * (reps + 1))[:-1]
        result = []
        for i in range(1, len(layers_str) + 1):
            count = 0
            block_str = layers_str[:i]
            if block_str[-1] == "C":
                pass
            else:
                qc = copy.deepcopy(self.qc)
                num_rotation_layers = len(block_str.replace("C", ""))
                param_vector = ParameterVector(
                    f"SU2_block", num_rotation_layers * ip_number
                )
                for j, layer in enumerate(block_str):
                    if layer == "C":
                        SU2.append_cnot_layer(qc, ip_number, self.entanglement)
                        qc.barrier()
                    else:
                        params = param_vector[
                            ip_number * count : ip_number * (count + 1)
                        ]
                        SU2.append_r_layer(qc, params, layer)
                        count += 1
                if block_str[-1] == "Y":
                    SU2.rotate_psi(qc, ip_number)
                result.append(qc)
        return result

    def construct_natgrad_blocks_2(self, ip_number, reps):    # 这个方法用于构造自然梯度优化块
        layers_str = ("YC" * (reps + 1))[:-1]     # 生成了一个包含重复层次的字符串
        result = []        # 存储构造的量子块
        # print(layers_str)
        qc = copy.deepcopy(self.qc)
        for i in range(1, len(layers_str) + 1):     # 使用for循环便利每个层次结构，并且进行构建
            count = 0
            block_str = layers_str[:i]         # 从layers_str中选择一部分
            if block_str[-1] == "C":       # 如果block_str最后一个字符为C，则跳过
                pass
            else:                          # block_str最后一个字符不是C，则继续构造量子块
                qc = copy.deepcopy(self.qc)      # 复制现有的量子电路qc
                num_rotation_layers = len(block_str.replace("C", ""))-1   # 计算旋转层的数量num_rotation_layers
                param_vector = ParameterVector(                  # 创建一个参数向量param_vector，用于保存旋转层的参数
                    f"SU2_block", num_rotation_layers * ip_number
                )
                for j, layer in enumerate(block_str):     # 遍历这block_str中的每一层
                    if layer == "C":          # 如果layer等于c
                        SU2.append_cnot_layer(qc, ip_number, self.entanglement)   # 在qc中添加cnot门
                        qc.barrier()      # 并且插入障碍
                    else:
                        params = param_vector[
                            ip_number * count : ip_number * (count + 1)  # 添加旋转层
                        ]
                        SU2.append_r_layer(qc, params, layer)    # 分配参数给旋转层，并且调用append_r_layer方法
                        count += 1   #更新计数器
                if block_str[-1] == "Y":    # block_str：如果block_str最后一个字符是Y
                    SU2.rotate_psi(qc, ip_number)    #调用rotate_psi的方法，旋转量子态
                # print(qc)
                result.append(qc)    #  将构建的量子电路块qc添加到结果列表中

        # for qc in result:
        #     qc = qc.assign_parameters(np.zeros(qc.num_parameters), inplace=False)
        #     print(qc)
        #     print("================================================\n")

        return result

    @staticmethod   # 这是一个静态方法，用于在量子电路qc中添加旋转门
    def append_r_layer(qc, params, layer):      # params是旋转门参数，layer是当前层的类型
        for i, param in enumerate(params):     # 使用enumerate方法遍历params列表，i表示索引，param表示当前的参数值
            if layer == "Y":     #如果layer是Y，则在量子比特i上应用RY旋转门，旋转角度为param
                qc.ry(param, i)   # 表示使用参数param对量子比特i进行Y轴的旋转
            elif layer == "Z":   #如果layer是Z，则在量子比特i上应用RZ旋转门，旋转角度为param
                qc.rz(param, i)  # 表示使用参数param对量子比特i进行Z轴的旋转
            else:      # 如果这个layer不是y或者z，它会抛出一个ValueError。表示这个层不是一个有效的旋转y或者z门
                raise ValueError("Layer not a rotation Y-/Z-Gate")

    @staticmethod  # 定义一个静态方法append_cnot_layer，用于向量子电路（qc）中添加不同的类型cnot门层。cnot门用于量子比特之间的纠缠
    def append_cnot_layer(qc, ip_number, entanglement):  #qc 量子电路，ip_number：量子比特数目，entanglement：纠缠类型
        cnot_circular = True    # 设定一个标志变量cnot_circular，默认为True，用于控制是否要实现cnot的连接
        if entanglement == "circular":    # 如果纠缠类型是circular，环形结构首尾纠缠，中间也纠缠
            if cnot_circular:     # 检查cnot_circular是否为True，如果为True就是继续执行下边代码
                qc.cnot(ip_number - 1, 0)    # 量子电路qc中添加了cnot门，从最后一个量子比特（索引为ip_number - 1）
            for i in range(ip_number - 1):  # Remove -1 for circular
                qc.cnot(i, (i + 1))
            # for i in range(ip_number):
            #    qc.cnot(i, (i + 1) % ip_number)
        elif entanglement == "full":      # 如果纠缠类型为full，则执行下边代码块。完全纠缠
            for i in range(ip_number - 1):   # 遍历量子比特索引0到ip_number - 2 。  ，，
                for j in range(i + 1, ip_number):
                    qc.cnot(i, j)

        elif entanglement == "rev": # 反向纠缠
            for i in range(ip_number - 2, 0):  # Remove -1 for circular   # 量子比特它会以反向顺序依次连接直到第一个量子比特。
                qc.cnot(i, i + 1)

    @staticmethod  # 静态方法对量子电路qc执行一系列的旋转操作
    def rotate_psi(qc, ip_number):  # 
        for i in range(ip_number):   # 使用for循环，遍历每个量子比特，从索引0到ip_number-1，
            qc.sdg(i)     # 对每个量子比特i去施加s共轭转置门，改变量子态的相位
            qc.h(i)       # 对每个量子比特i去施加h门，把量子比特从标准基态转化为均匀的叠加态



SU2(2, reps=2)
# FAIL = False
#
#
# class ParameterTheta:
#     def __init__(self, _name, _value: float = 0.0):
#         self.value = ParameterTheta.check_boundaries(_value)
#         self.name = _name
#         self.gate = None
#
#     def add_resp_gate(self, _gate):
#         self.gate = _gate
#
#     def alter_value(self, _new_value):
#         self.value = self.check_boundaries(_new_value)
#         self.gate.params[0] = _new_value
#
#     @staticmethod
#     def check_boundaries(_value):
#         if (_value >= (np.pi * 2)) | (_value < (- np.pi * 2)):
#             raise ValueError("Value for angle theta does not obey the boundaries of -2Pi to 2Pi")
#         else:
#             return _value
#
#
# _main_number = 6
# _ip1 = QuantumRegister(_main_number, 'IP1')
# _qc = QuantumCircuit(_ip1)
#
# c_nots = []
# for i in range(_main_number):
#     for j in range(i+1, _main_number):
#         print(i, j)
#         c_nots.append((i, j))
#
# param_vector = []
# reps = 1
# count = 0
# for rep in range(reps):
#     for i in range(_main_number):
#         theta = ParameterTheta(f"Theta[{count}]")
#         _qc.ry(theta.value, _ip1[i])
#         last_instruction = _qc.Data[-1]
#         gate = last_instruction[0]
#         theta.add_resp_gate(gate)
#         param_vector.append(theta)
#         count += 1
#
#     for i in range(_main_number):
#         theta = ParameterTheta(f"Theta[{count}]")
#         _qc.rz(theta.value, _ip1[i])
#         last_instruction = _qc.Data[-1]
#         gate = last_instruction[0]
#         theta.add_resp_gate(gate)
#         param_vector.append(theta)
#         count += 1
#
#     for c_not in c_nots:
#         _qc.cx(c_not[0], c_not[1])
#
#
#
# _ip2 = QuantumRegister(_main_number, 'IP1')
# _qc2 = QuantumCircuit(_ip2)
# _qc.cx(_ip2[0], _ip2[2])
# _qc.compose(_qc2)
# # theta1.alter_value(2)
#
# for param in param_vector:
#     param.alter_value(3)
# # for gate in _qc.Data:
# #     # print(gate[0].params)
# #     if len(gate[0].params) > 0:
# #         print("Here")
# #         gate[0].params[0] = 2
# #         print(type(gate[0].params[0]))
#
# # for gate in _qc.Data:
# #     print(gate[0].params)
# _qc.draw(output='mpl', style={'backgroundcolor': '#EEEEEE'}, filename=f"su2_selfmade")
