import numpy as np
import itertools

from scipy import linalg


def contractMPS(MPS):
    n = len(MPS)
    results = []
    # for binary_string in list(itertools.product("10", repeat=n)).__reversed__():
    # for binary_string in itertools.product("01", repeat=n):
    #     for m in range(n - 1):
    #         phy_index = int(binary_string[m])
    #         phy_index_po = int(binary_string[m + 1])
    #         if m == 0:
    #             result = MPS[m][phy_index]
    #         A_mpo = MPS[m + 1]
    #         result = np.einsum("jk, kl-> jl", result, A_mpo[phy_index_po])
    #
    #     result = result[0, 0]
    #     results.append(result)

    results = []
    for binary_string in itertools.product("01", repeat=n):
        result = [1]
        for m, bit in enumerate(binary_string):
            result = result @ MPS[m][int(bit)]

        result = result[0]
        results.append(result)

    return np.asarray(results)


def qr_decompose(MPS):
    for m in range(len(MPS) - 1):
        A_m = MPS[m]
        reshaped = np.reshape(A_m, (-1, A_m.shape[2]))
        qr_m = linalg.qr(reshaped, mode="economic")
        q, r = qr_m[0], qr_m[1]
        A_m_star = q.reshape((2, -1, q.shape[1]))
        MPS[m] = A_m_star
        A_mpo = MPS[m + 1]
        A_mpo_tilde = np.einsum("ij, kjm-> kim", r, A_mpo)
        MPS[m + 1] = A_mpo_tilde
        # print("IN QR", MPS[m + 1].shape)
    return MPS


def svd_decompose_mps(MPS, dim: int = 2):
    print(f"Doing SVD with dim {dim}")
    for i in range(len(MPS) - 1):
        A_m = MPS[i]
        reshaped = np.reshape(A_m, (-1, A_m.shape[2]))
        svd_m = linalg.svd(reshaped, full_matrices=False)
        u, s, vh = svd_m[0], np.diag(svd_m[1]), svd_m[2]
        if i == 0:
            scale_factor = 1
        else:
            scale_factor = np.power(np.sum((svd_m[1][:2]) ** 2), -1 / 2)
        u_tilde, s_tilde, vh_tilde = u[:, :dim], s[:dim, :dim], vh[:dim, :]
        # TODO: maybe implement a check if right/left-normalised, assert..
        u_tilde_reshaped = u_tilde.reshape((2, -1, u_tilde.shape[1]))
        svh = np.einsum("ij, jk-> ik", s_tilde, vh_tilde)
        MPS[i] = u_tilde_reshaped
        next_mps_tilde = np.einsum("ij, kjm-> kim", svh, MPS[i + 1])
        MPS[i + 1] = next_mps_tilde
    return MPS


#
# def pad_qr_mps(MPS, chi):
#     for i in range(len(MPS)):
#         # print(MPS[i].shape)
#         left_index = min(2 ** i, chi)
#         if i == (len(MPS) - 1):
#             right_index = 1
#         else:
#             right_index = min(2 ** (i + 1), chi)
#         MPS[i] = np.pad(
#             MPS[i],
#             pad_width=(
#                 (0, 0),
#                 (0, left_index - MPS[i].shape[1]),
#                 (0, right_index - MPS[i].shape[2]),
#             ),
#         )


def pad_mps(MPS, chi):  # Pad to needed bond dimension
    MPS[0] = np.pad(
        MPS[0],
        pad_width=(
            (0, 0),
            (0, 0),
            (0, chi - MPS[0].shape[2]),
        ),
    )

    for i in range(1, len(MPS) - 1):
        MPS[i] = np.pad(
            MPS[i],
            pad_width=(
                (0, 0),
                (0, chi - MPS[i].shape[1]),
                (0, chi - MPS[i].shape[2]),
            ),
        )

    MPS[-1] = np.pad(
        MPS[-1],
        pad_width=(
            (0, 0),
            (0, chi - MPS[-1].shape[1]),
            (0, 0),
        ),
    )


def contract_start_tensors(MPS, s):
    MPS_new = []
    for i, mps in enumerate(MPS):
        if s == 1:
            MPS_new.append(mps)

        elif s == 2:
            if i == 0:
                MPS[1] = np.einsum("ijk, mkl-> mijl", MPS[0], MPS[1])
                MPS_new.append(MPS[1])

            elif i == 1:
                pass

            else:
                MPS_new.append(mps)

        elif s == 3:
            if i == 0:
                MPS[1] = np.einsum("ijk, mkl-> mijl", MPS[0], MPS[1])

            elif i == 1:
                MPS[2] = np.einsum("mijk, qkl-> qmijl", MPS[1], MPS[2])
                MPS_new.append(MPS[2])

            elif i == 2:
                pass

            else:
                MPS_new.append(mps)

    return MPS_new
