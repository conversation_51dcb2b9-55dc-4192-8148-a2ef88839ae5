import numpy as np

from qiskit.extensions import UnitaryGate
from scipy.linalg import null_space


def build_mpd_unitaries_from_mps(MPS, chi, d=2):
    unitaries = []
    length_mps = len(MPS)
    for j in range(length_mps):
        A_n = MPS[j]
        if j == 0:
            mps_mat = A_n.reshape((-1, chi))
            unitary_mat = mps_mat
            unitary = UnitaryGate(unitary_mat, label=f"G[{j}]")
            unitaries.append(unitary)
        elif j == (length_mps - 1):
            mps_mat = A_n.reshape((-1, A_n.shape[2]))
            unitary = np.zeros((d, d, chi, chi))
            null_mat = null_space(np.transpose(mps_mat))
            null_tensor = null_mat.reshape((2, -1, null_mat.shape[1]))
            unitary[0, :, :, 0] = mps_mat.reshape(d, chi)
            unitary[1, :, :, 0] = null_tensor[:, :, 0]
            for i in range(0, int((d * chi - 2) / 2)):
                unitary[0, :, :, i + 1] = null_tensor[:, :, 1 + i * 2]
                unitary[1, :, :, i + 1] = null_tensor[:, :, 2 + i * 2]
            # print("EINSUM", np.einsum("ijkl, mjko -> imlo", unitary, unitary)[0, 0, 0, 1])

            # physical, physical, virtual, virtual -> physical, virtual, physical, virtual
            unitary = np.einsum("ijkl-> jkli", unitary)
            unitary_mat = unitary.reshape((d * chi, d * chi))
            unitary = UnitaryGate(unitary_mat, label=f"G[{j}]")
            unitaries.append(unitary)

        else:
            mps_mat = A_n.reshape((-1, A_n.shape[2]))
            unitary = np.zeros((d, d, chi, chi))
            null_mat = null_space(np.transpose(mps_mat))
            null_tensor = null_mat.reshape((2, -1, null_mat.shape[1]))
            unitary[0, :, :, :] = A_n
            unitary[1, :, :, :] = null_tensor

            unitary = np.einsum(
                "ijkl-> jkli", unitary
            )  # phys, phys, virt, virt -> phys, virt, phys, virt  "ilkj"
            unitary_mat = unitary.reshape((chi * d, chi * d))
            unitary = UnitaryGate(unitary_mat, label=f"G[{j}]")
            unitaries.append(unitary)

    return unitaries
