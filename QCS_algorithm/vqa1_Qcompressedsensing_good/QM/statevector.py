import numpy as np
import scipy
import ast
import os

class Statevector:
    def __init__(self, amplitudes, x_coordinates=None, epsilon: float = 1e-3, ignore_norm: bool = False):
        self.epsilon = epsilon
        if not ignore_norm:
            self.check_normalization(amplitudes)
        if type(x_coordinates) == np.ndarray:
            self._x_coordinates = x_coordinates
            self._hn = self._x_coordinates[1] - self._x_coordinates[0]
        else:
            number_values = len(np.asarray(amplitudes))
            self._x_coordinates, self._hn = np.linspace(
                -1, 1, number_values, endpoint=False, retstep=True
            )
        self._n = int(np.log2(len(self._x_coordinates)))
        self._dim = self._x_coordinates.shape
        self._amplitudes = np.asarray(amplitudes)

    def check_normalization(self, amplitudes):
        norm = scipy.linalg.norm(amplitudes)
        if (norm - 1) > self.epsilon:
            raise ValueError(
                f"Wavefunction is not properly normalised, norm is: {norm}\nand amplitudes are {amplitudes}"
            )

    def get_x_coordinates(self):
        return self._x_coordinates

    def get_amplitudes(self):
        return self._amplitudes

    def get_amplitudes_bra(self):
        return np.conjugate(self._amplitudes).T

    def get_real_part(self):
        return np.real(self._amplitudes)

    def get_imag_part(self):
        return np.imag(self._amplitudes)

    def get_hn(self):
        return self._hn

    def to_interval(self):
        self._amplitudes = self._amplitudes / np.sqrt(self._hn)
        return self._amplitudes

    def get_prob_density(self):
        return np.power(np.abs(self._amplitudes), 2)

    def get_fidelity_with_target(self, target: "Wavefunction"):
        overlap = np.dot(np.conj(self._amplitudes), target.get_amplitudes())
        fidelity = np.power(np.abs(overlap), 2)
        return fidelity

    def get_overlap_with_target(self, target: "Wavefunction"):
        overlap = np.dot(np.conj(self._amplitudes), target.get_amplitudes())
        return overlap

    def create_projector_matrix(self):
        # Create |psi><psi| where |psi> = |V>
        projector_matrix = np.einsum("i,j", self._amplitudes, self._amplitudes)
        print("PROJECTOR", projector_matrix)
        return projector_matrix

    @classmethod
    def add(cls, wavefunc_1, wavefunc_2):
        if wavefunc_1.get_x_coordinates() != wavefunc_2.get_x_coordinates():
            raise ValueError(
                "Wavefunctions to be added are not defined on the same coordinate set"
            )
        return Statevector(
            wavefunc_1.get_amplitudes() + wavefunc_2.get_amplitudes(),
            wavefunc_1.get_x_coordinates(),
        )

    @classmethod
    def multiply(cls, wavefunc_1, factor):
        # print(np.linalg.norm(wavefunc_1.get_amplitudes()*factor))
        return Statevector(
            wavefunc_1.get_amplitudes() * factor,
            wavefunc_1.get_x_coordinates(),
        )

    @classmethod
    def init_plainwave(cls, ip_number, x_range: tuple = (-1, 1), k: float = np.pi):
        number_values = 2 ** ip_number
        x_coordinates = np.linspace(
            x_range[0], x_range[1], number_values, endpoint=False
        )
        amplitudes = [np.exp(1j * k * x) for x in x_coordinates]
        alpha = scipy.linalg.norm(amplitudes)  # Absolute was not there before
        amplitudes_normalised = np.asarray(amplitudes) / alpha
        return Statevector(amplitudes_normalised, x_coordinates)

    @classmethod
    def init_cos(cls, ip_number, x_range: tuple = (-1, 1), k: float = np.pi):
        number_values = 2 ** ip_number
        x_coordinates = np.linspace(
            x_range[0], x_range[1], number_values, endpoint=False
        )
        amplitudes = [np.cos(k * x) for x in x_coordinates]
        alpha = scipy.linalg.norm(amplitudes)  # Absolute was not there before
        amplitudes_normalised = np.asarray(amplitudes) / alpha
        return Statevector(amplitudes_normalised, x_coordinates)


    @classmethod
    def init_const(cls, ip_number, x_range: tuple = (-1, 1)):
        number_values = 2 ** ip_number
        x_coordinates = np.linspace(
            x_range[0], x_range[1], number_values, endpoint=False
        )
        amplitudes = [1 for x in x_coordinates]
        alpha = scipy.linalg.norm(amplitudes)  # Absolute was not there before
        # print("alpha", alpha)
        amplitudes_normalised = np.asarray(amplitudes) / alpha
        # print(scipy.linalg.norm(amplitudes_normalised))
        # print(amplitudes_normalised)
        return Statevector(amplitudes_normalised, x_coordinates)

    @classmethod
    def ho_groundstate(cls, ip_number, x_range: tuple = (-1, 1), omega: float = 7):
        number_values = 2 ** ip_number
        x_coordinates = np.linspace(
            x_range[0], x_range[1], number_values, endpoint=False
        )
        amplitudes = [
            np.power(omega / np.pi, 1 / 4) * np.exp(-omega / 2 * x ** 2)
            for x in x_coordinates
        ]
        alpha = scipy.linalg.norm(amplitudes)  # Absolute was not there before
        amplitudes_normalised = np.asarray(amplitudes) / alpha
        return Statevector(amplitudes_normalised, x_coordinates)

    @staticmethod
    def get_normalization_factor(amplitudes):
        norm = scipy.linalg.norm(amplitudes)
        return norm


class Potential(Statevector):
    def __init__(self, num_ip, x_range: tuple = (-1, 1), potential_dict: dict = None):
        self.kind = None
        self.alpha = None
        self.params = None
        self.off = False
        x_arr, amplitudes = self.construct(num_ip, x_range, potential_dict)
        super().__init__(amplitudes=amplitudes, x_coordinates=x_arr, ignore_norm=self.off)

    def get_potential(self):
        return self.alpha*self.get_amplitudes()

    def construct(self, num_ip, x_range, potential_dict):
        num_vals = 2 ** num_ip
        x_coordinates = np.linspace(
            x_range[0], x_range[1], num_vals, endpoint=False
        )
        len_int = (x_range[1]-x_range[0])
        if not potential_dict:
            raise ValueError("No potential dictionary given")

        self.kind = list(potential_dict.keys())[0]
        self.params = potential_dict[self.kind]
        self.off = (list(self.params.values())[0] == 0)
        if self.off:
            amplitudes = np.zeros(num_vals)
            self.alpha = 0
        else:
            if self.kind == "HARMONIC":
                amplitudes = np.array([1 / 2 * self.params["OMEGA"]**2 * x ** 2 for x in x_coordinates])

            elif self.kind == "BICHROMATIC":
                amplitudes = np.array([self.params["S1"]*np.sin(self.params["KAPPA1"]*(x+self.params["Offset"]*len_int)) +
                                       self.params["S2"]*np.sin(self.params["KAPPA2"]*(x+self.params["Offset"]*len_int))
                                       for x in x_coordinates])
                print("HERE in potential: ", amplitudes)

            else:
                raise ValueError(f"Type {self.kind} not implemented!")

            self.alpha = scipy.linalg.norm(amplitudes)
            amplitudes /= self.alpha
        return x_coordinates, amplitudes


# pot = "{'HARMONIC': {'OMEGA': 10}}"
# pot_dict = ast.literal_eval(pot)
# pot = Potential(4, potential_dict=pot_dict)
# print(pot.get_potential())
# print(pot.kind)

