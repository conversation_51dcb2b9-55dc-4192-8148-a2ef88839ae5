
import numpy as np
from qiskit.quantum_info import Statevector
from qiskit import QuantumCircuit

class QuantumOptimizer:
    def __init__(self, method='SPSA', max_iter=100, learning_rate=0.1):
        """优化器初始化。

        Args:
            method (str): 优化方法，默认为 'SPSA'。
            max_iter (int): 最大迭代次数，默认为 100。
            learning_rate (float): 学习率，默认为 0.1。
        """
        self.method = method
        self.max_iter = max_iter
        self.learning_rate = learning_rate

    def optimize(self, network, initial_params, measured_data=None):
        """优化量子网络参数。

        Args:
            network: 量子网络对象，包含量子电路 (qc) 和变分参数 (ansatz)。
            initial_params (list): 初始参数列表。
            measured_data (dict, optional): 测量数据，用于监督优化。

        Returns:
            np.ndarray: 优化后的参数。
        """
        params = np.array(initial_params, dtype=float)
        
        for _ in range(self.max_iter):
            if measured_data is not None:
                # 使用测量数据进行监督优化
                cost = network.compute_cost(params, measured_data)
                gradients = self._compute_gradient(network.qc, params, 
                                                   network.ansatz.freq_params + network.ansatz.damp_params, 
                                                   measured_data)
                params -= self.learning_rate * gradients
            else:
                # 无监督优化，使用熵作为目标
                bound_circuit = network.qc.assign_parameters(
                    {network.ansatz.params[i]: params[i] for i in range(len(params))}
                )
                state = Statevector.from_instruction(bound_circuit)
                probs = np.abs(state.data) ** 2
                cost = -np.sum(probs * np.log(probs + 1e-10))  # 计算熵
                gradients = self._compute_gradient(network.qc, params, network.ansatz.params)
                params -= self.learning_rate * gradients
        return params

    def _compute_gradient(self, circuit, params, param_list, measured_data=None):
        """计算梯度（数值梯度实现）。

        Args:
            circuit (QuantumCircuit): 量子电路。
            params (np.ndarray): 当前参数值。
            param_list (list): 参数符号列表。
            measured_data (dict, optional): 测量数据。

        Returns:
            np.ndarray: 梯度向量。
        """
        epsilon = 1e-3
        gradients = np.zeros(len(params))
        
        for i in range(len(params)):
            # 前向扰动
            params_plus = params.copy()
            params_plus[i] += epsilon
            circuit_plus = circuit.assign_parameters(
                {param_list[j]: params_plus[j] for j in range(len(params))}
            )
            state_plus = Statevector.from_instruction(circuit_plus)  # 修复后的正确调用
            probs_plus = np.abs(state_plus.data) ** 2
            
            # 后向扰动
            params_minus = params.copy()
            params_minus[i] -= epsilon
            circuit_minus = circuit.assign_parameters(
                {param_list[j]: params_minus[j] for j in range(len(params))}
            )
            state_minus = Statevector.from_instruction(circuit_minus)
            probs_minus = np.abs(state_minus.data) ** 2
            
            if measured_data is not None:
                # 监督优化：最小化与测量数据的误差
                cost_plus = np.sum((probs_plus - measured_data) ** 2)
                cost_minus = np.sum((probs_minus - measured_data) ** 2)
            else:
                # 无监督优化：最大化熵
                cost_plus = -np.sum(probs_plus * np.log(probs_plus + 1e-10))
                cost_minus = -np.sum(probs_minus * np.log(probs_minus + 1e-10))
            
            # 数值梯度
            gradients[i] = (cost_plus - cost_minus) / (2 * epsilon)
        return gradients