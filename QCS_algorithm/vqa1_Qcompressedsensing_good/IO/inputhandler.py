import numpy as np
import os
import ast

def create_scripts_for_parameter_scan():
    parameters = {
        "n": 4,
        "k": 0,
        "reps": 3,
        "optimizer": "ADAM",
        "ADAM_IT": 5,
        "BETA_1": 0.999,
        "BETA_2": 0.98,
        "ETA": 0.001,
    }


SHELL_SCRIPT_STR = """#$ -q idefix.q
#$ -cwd
#$ -pe smp 1
#$ -l h_vmem=4.0G
#$ -l h_cpu=72:00:00
#$ -j y
#$ -m beas
#$ -M <EMAIL>
#$ -N vqa
#$ -o vqa.output

export PYTHONPATH='$PYTHONPATH:/afs/physnet.uni-hamburg.de/users/las_vqs/nvanhuel/Schreibtisch/Masterthesis/Code/vqa/'
. /afs/physnet.uni-hamburg.de/users/las_vqs/nvanhuel/Schreibtisch/Masterthesis/miniconda3/bin/activate
conda activate vqa

python3 /afs/physnet.uni-hamburg.de/users/las_vqs/nvanhuel/Schreibtisch/Masterthesis/Code/vqa/vqa/run.py
echo job finished
"""


def cast_attributes(attrs):
    for key_parent, val_parent in attrs.items():
        for key, val in val_parent.items():
            if isinstance(val, str):
                if "{" in val:
                    attrs[key_parent][key] = ast.literal_eval(val)
                    continue
            try:
                attrs[key_parent][key] = (
                    float(val) if not float(val).is_integer() else int(val)
                )
            except ValueError:
                pass

    if attrs["NSP"]["OMEGA_HO"] != 0:
        print("Updating Potential dict")
        attrs["NSP"]["POTENTIAL"]["HARMONIC"]["OMEGA"] = attrs["NSP"]["OMEGA_HO"]


class InputHandler:
    NSP = {
        "N": 4,
        "REPS": 1,
        "ANSATZ": "SU2",
        "TERMS": "K",
        "OMEGA_HO": 0,
        "POTENTIAL": "{'HARMONIC': {'OMEGA': 0}}",
        "G": 0,
        "LAYER_SCHEME": "YZ",
    }

    MSP = {"SHOTS": 1024, "POVM_IT": -1}

    OSP = {
        "SPSA_IT": 10,
        "OPTIMIZER": "ADAM",
        "ADAPT": "NO",
        "IT": 10,
        "BETA1": 0.9,
        "BETA2": 0.999,
        "EPSILON": 1e-8,
        "LR": 0.01,
        "START": 100,
        "ALPHA": 0.75,
        "OMEGA": 10,
        "GAMMA": 0.5,
        "NUM_FIT": 10,
        "NUM_OFF": 5,
        "NATGRAD": "NO"
    }

    def __init__(self):
        self.filename = f"{os.getcwd()}/input.txt"
        self.settings = None
        self.used_settings = {}

    # def write(self, attributes: dict):
    #     input_str = "GENERAL\n"
    #     for k, v in InputHandler.GENERAL.items():
    #         if k in attributes.keys():
    #             input_str += f"""{k}={attributes[k]}"""
    #         else:
    #             input_str += f"""{k}={v}"""
    #         input_str += """\n"""
    #
    #     input_str += "\nHYPERPARAMETERS\n"
    #     for key in attributes:
    #         if key not in InputHandler.GENERAL:
    #             input_str += f"""{key}={attributes[key]}"""
    #         input_str += """\n"""
    #     with open(self.filename, "w+") as f:
    #         f.write(input_str)

    def read(self):
        attrs_from_file = {"NSP": InputHandler.NSP, "MSP": InputHandler.MSP, "OSP": InputHandler.OSP}
        lines = open(self.filename).read().splitlines()
        index_msp = [i for i, v in enumerate(lines) if "MSP" in v][0]
        index_osp = [i for i, v in enumerate(lines) if "OSP" in v][0]
        for j, line in enumerate(lines):
            if "=" in line:
                if line[0] == "#":
                    continue
                key, val = line.split("=")
                if j < index_msp:
                    attrs_from_file["NSP"].update({key: val})
                elif j < index_osp:
                    attrs_from_file["MSP"].update({key: val})
                else:
                    attrs_from_file["OSP"].update({key: val})

        cast_attributes(attrs_from_file)
        self.settings = attrs_from_file

    def get(self, key, cat="NSP"):
        if key in self.settings[cat].keys():
            val = self.settings[cat][key]
            self.used_settings.update({key: val})
            return val
        else:
            if cat == "NSP":
                val = InputHandler.NSP[key]
                self.settings[cat].update({key: val})
                self.used_settings.update({key: val})
                return val
            elif cat == "MSP":
                val = InputHandler.MSP[key]
                self.settings[cat].update({key: val})
                self.used_settings.update({key: val})
                return val
            elif cat == "OSP":
                val = InputHandler.OSP[key]
                self.settings[cat].update({key: val})
                self.used_settings.update({key: val})
                return val
            else:
                raise ValueError(f"No default value for the key {key} known!")


class ShellScriptHandler:
    def __init__(self):
        self.filename = "../../runcode.sh"

    def write(self):
        with open(self.filename, "w+") as f:
            f.write(SHELL_SCRIPT_STR)


# ih = InputHandler()
# attrs = ih.read()
# a = list(attrs["General"]["TERMS"])
# print(a)
