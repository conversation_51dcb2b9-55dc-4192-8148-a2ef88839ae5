import logging
import logging.handlers
import os
import sys
from mpi4py import MPI


comm = MPI.COMM_WORLD
rank = comm.Get_rank()


logging.basicConfig(
    level=logging.WARNING, format=f"[%(name)s] :: [%(levelname)s] :: [%(message)s]"
)


def get_logger(name="root"):
    logger = logging.getLogger(name)
    try:
        level = os.environ["LEVEL"]
    except:
        print("Falling back to default info")
        level = "INFO"
    logger.setLevel(level)  # logging.INFO)
    fmt = f"[%(levelname)s][%(name)s][rank={rank}][%(message)s]"
    formatter = logging.Formatter(fmt)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    # file_handler = logging.FileHandler('debug.log', mode='w')
    # file_handler.setFormatter(formatter)

    logger.addHandler(console_handler)
    # logger.addHandler(file_handler)
    logger.propagate = False
    return logger
