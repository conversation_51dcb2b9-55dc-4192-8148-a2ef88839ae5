
from qiskit import QuantumCircuit
from qiskit.quantum_info import Statevector
import numpy as np

def compute_gradient(qc, params, param_list):
    gradients = []
    eps = np.pi / 4
    for i, param in enumerate(param_list):
        shifted_plus = params.copy()
        shifted_plus[i] += eps
        plus_dict = {param_list[j]: shifted_plus[j] for j in range(len(params))}
        plus_circuit = qc.assign_parameters(plus_dict)
        plus_state = Statevector.from_instruction(plus_circuit)
        
        shifted_minus = params.copy()
        shifted_minus[i] -= eps
        minus_dict = {param_list[j]: shifted_minus[j] for j in range(len(params))}
        minus_circuit = qc.assign_parameters(minus_dict)
        minus_state = Statevector.from_instruction(minus_circuit)
        
        gradient = (np.sum(np.abs(plus_state.data) ** 2) - np.sum(np.abs(minus_state.data) ** 2)) / (2 * eps)
        gradients.append(gradient)
    return np.array(gradients)