
from qiskit import QuantumCircuit
from qiskit.circuit import Parameter

class SEMAAnsatz:
    def __init__(self, num_qubits, num_peaks, iter_max):
        self.num_qubits = num_qubits
        self.num_peaks = num_peaks
        self.iter_max = iter_max
        self.freq_params = [Parameter(f'freq_{i}') for i in range(num_peaks)]
        self.damp_params = [Parameter(f'damp_{i}') for i in range(num_peaks)]
        self.circuit = self._build_circuit()

    def _build_circuit(self):
        qc = QuantumCircuit(self.num_qubits)
        for _ in range(self.iter_max):
            for i in range(self.num_peaks):
                qubit = i % self.num_qubits
                qc.rz(self.freq_params[i], qubit)
                qc.ry(self.damp_params[i], qubit)
            qc.barrier()
        return qc

class PeakFinderAnsatz:
    def __init__(self, num_qubits):
        self.num_qubits = num_qubits
        self.circuit = self._build_circuit()

    def _build_circuit(self):
        qc = QuantumCircuit(self.num_qubits)
        qc.h(range(self.num_qubits))
        qc.x(range(self.num_qubits))
        qc.h(self.num_qubits - 1)
        qc.mcx(list(range(self.num_qubits - 1)), self.num_qubits - 1)
        qc.h(self.num_qubits - 1)
        qc.x(range(self.num_qubits))
        qc.h(range(self.num_qubits))
        qc.x(range(self.num_qubits))
        qc.h(self.num_qubits - 1)
        qc.mcx(list(range(self.num_qubits - 1)), self.num_qubits - 1)
        qc.h(self.num_qubits - 1)
        qc.x(range(self.num_qubits))
        qc.h(range(self.num_qubits))
        return qc

class FisherAnsatz:
    def __init__(self, num_qubits, num_params):
        self.num_qubits = num_qubits
        self.params = [Parameter(f'p_{i}') for i in range(num_params)]
        self.circuit = self._build_circuit()

    def _build_circuit(self):
        qc = QuantumCircuit(self.num_qubits)
        for i, param in enumerate(self.params):
            qc.rx(param, i % self.num_qubits)
        return qc

class OptSampleAnsatz:
    def __init__(self, num_qubits):
        self.num_qubits = num_qubits
        self.params = [Parameter(f'opt_{i}') for i in range(num_qubits)]
        self.circuit = self._build_circuit()

    def _build_circuit(self):
        qc = QuantumCircuit(self.num_qubits)
        for i, param in enumerate(self.params):
            qc.ry(param, i)
        return qc