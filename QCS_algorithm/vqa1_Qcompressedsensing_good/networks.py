
from ansatz import SEMAAnsatz, PeakFinderAnsatz, FisherAnsatz, OptSampleAnsatz
from qiskit import QuantumCircuit
from qiskit.quantum_info import Statevector
import numpy as np

class SEMANetwork:
    def __init__(self, num_qubits, num_peaks, iter_max):
        self.num_qubits = num_qubits
        self.qc = QuantumCircuit(num_qubits)
        self.ansatz = SEMAAnsatz(num_qubits, num_peaks, iter_max)
        self.qc.append(self.ansatz.circuit, range(num_qubits))

    def compute_cost(self, params, measured_data):
        param_dict = {self.ansatz.freq_params[i]: params[i] for i in range(self.ansatz.num_peaks)}
        param_dict.update({self.ansatz.damp_params[i]: params[i + self.ansatz.num_peaks] 
                          for i in range(self.ansatz.num_peaks)})
        bound_circuit = self.qc.assign_parameters(param_dict)
        state = Statevector.from_instruction(bound_circuit)
        probs = np.abs(state.data) ** 2
        return np.sum((probs - measured_data) ** 2)  # 调整为概率误差

class PeakFinderNetwork:
    def __init__(self, num_qubits):
        self.num_qubits = num_qubits
        self.qc = QuantumCircuit(num_qubits)
        self.ansatz = PeakFinderAnsatz(num_qubits)
        self.qc.append(self.ansatz.circuit, range(num_qubits))

    def find_peak(self):
        self.qc.measure_all()
        return self.qc

class FisherNetwork:
    def __init__(self, num_qubits, num_params):
        self.num_qubits = num_qubits
        self.qc = QuantumCircuit(num_qubits)
        self.ansatz = FisherAnsatz(num_qubits, num_params)
        self.qc.append(self.ansatz.circuit, range(num_qubits))

    def compute_fisher(self, params):
        param_dict = {self.ansatz.params[i]: params[i] for i in range(len(params))}
        bound_circuit = self.qc.assign_parameters(param_dict)
        state = Statevector.from_instruction(bound_circuit)
        fisher_matrix = np.zeros((len(params), len(params)))
        eps = 1e-5
        for i in range(len(params)):
            shifted_params = params.copy()
            shifted_params[i] += eps
            shifted_dict = {self.ansatz.params[j]: shifted_params[j] for j in range(len(params))}
            shifted_circuit = self.qc.assign_parameters(shifted_dict)
            shifted_state = Statevector.from_instruction(shifted_circuit)
            overlap = np.abs(state.inner(shifted_state)) ** 2
            fisher_matrix[i, i] = 8 * (1 - overlap) / (eps ** 2)
        return fisher_matrix

class OptSampleNetwork:
    def __init__(self, num_qubits):
        self.num_qubits = num_qubits
        self.qc = QuantumCircuit(num_qubits)
        self.ansatz = OptSampleAnsatz(num_qubits)
        self.qc.append(self.ansatz.circuit, range(num_qubits))

    def optimize(self, params):
        param_dict = {self.ansatz.params[i]: params[i] for i in range(len(params))}
        bound_circuit = self.qc.assign_parameters(param_dict)
        state = Statevector.from_instruction(bound_circuit)
        probs = np.abs(state.data) ** 2
        return probs