import numpy as np
import itertools

# 这是一个函数的定义，函数名为 get_expectation_value_from_1d_counts，它接受一个参数 counts。
def get_expectation_value_from_1d_counts(counts):
    if "1" in counts.keys():   # 这里的 if 语句检查字典 counts 中是否包含键 "1"。
        shots = counts["0"] + counts["1"] # 如果counts中有键"1"，则计算总的测量次数shots，为"0"和"1"的出现次数之和。
        expectation_value = (1 * counts["0"] / shots) - (1 * counts["1"] / shots) # 计算期望值expectation_value
    else:
        shots = counts["0"] # 如果counts中没有键"1"，说明只有状态"0"。
        # 在这种情况下，期望值是 1 * counts["0"] / shots，结果为 1，因为所有的测量结果都是状态 "0"。
        expectation_value = 1 * counts["0"] / shots 
    return expectation_value # 函数返回计算出的期望值 expectation_value。


def get_statevector_from_ansatz(params, ansatz):
    ansatz.activate_save_statevector()
    ansatz.assign_parameters(params)
    state = ansatz.sim_statevector()
    return state


def get_param_mat(param_values):
    num_parameters = len(param_values)
    param_mat = np.vstack([param_values] * 2 * num_parameters)
    for i in range(num_parameters):
        param_mat[i, i] += np.pi / 2
        param_mat[(num_parameters + i), i] -= np.pi / 2  # Change back to Pi / 2

    return param_mat


def sigma_from_povm(counts, shots):
    possible_counts = counts.keys()
    sigma = 0
    for bit_str in possible_counts:
        if bit_str == "00":
            sigma += counts["00"] * 1
        elif bit_str == "01":
            sigma -= counts["01"] * 1
        elif bit_str == "10":
            pass
        else:
            sigma -= counts["11"] * 1
    return float(sigma / shots)


def get_probability_array(counts, shots):
    num_qubits = len(list(counts.keys())[0])
    prob_arr = np.zeros(2**num_qubits)
    for j, binary_tuple in enumerate(itertools.product("01", repeat=num_qubits)):
        binary_str = "".join(binary_tuple)
        if binary_str in counts.keys():
            prob_arr[j] = counts[binary_str] / shots

    return prob_arr


def compute_variance(omega_arr, prob_arr, shots):
    var = np.sqrt(
        np.real(
            (np.sum((omega_arr**2) * prob_arr) - (np.sum(omega_arr * prob_arr)) ** 2)
        )
        / shots
    )
    return var


def compute_generator_expectation_values(counts, shots):
    num_qubits = len(list(counts.keys())[0])
    k_i = np.zeros(num_qubits)
    k_ij = np.zeros((num_qubits, num_qubits))
    for i in range(num_qubits):
        for key in counts.keys():
            if key[-1 - i] == "0":
                # if key[i] == "0":
                k_i[i] += counts[key] / (shots * 2)
            else:
                k_i[i] -= counts[key] / (shots * 2)

    for i in range(num_qubits):
        for j in range(num_qubits):
            for key in counts.keys():
                if key[-1 - i] == key[-1 - j]:
                    # if key[i] == key[j]:
                    k_ij[i, j] += counts[key] / (shots * 4)
                else:
                    k_ij[i, j] -= counts[key] / (shots * 4)

    # print(k_i)
    # print(k_ij)
    return k_i, k_ij
