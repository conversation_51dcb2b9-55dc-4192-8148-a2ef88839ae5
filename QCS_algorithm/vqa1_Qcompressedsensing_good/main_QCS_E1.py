

from networks import SEMANetwork, PeakFinderNetwork, FisherNetwork, OptSampleNetwork
from optimization import QuantumOptimizer
from qiskit import Aer, execute
import numpy as np
import matplotlib.pyplot as plt

class QuantumExperimentalData2D:
    def __init__(self, num_qubits=6, num_peaks=2, iter_max=3, row=8, col=8, nFFT=64):
        self.num_qubits = num_qubits
        self.num_peaks = num_peaks
        self.iter_max = iter_max
        self.row = row
        self.col = col
        self.nFFT = nFFT
        assert row * col == 2 ** num_qubits, "row * col must equal 2^num_qubits"
        self.sema_net = SEMANetwork(num_qubits, num_peaks, iter_max)
        self.peak_net = PeakFinderNetwork(num_qubits)
        self.fisher_net = FisherNetwork(num_qubits, num_peaks * 2)
        self.opt_net = OptSampleNetwork(num_qubits)
        self.optimizer = QuantumOptimizer(max_iter=50, learning_rate=0.1)

    def run(self, measured_data, t1, t2):
        """
        输入：
            measured_data: 归一化的信号数据，shape (2**num_qubits,)，如 (64,)
            t1: 时间索引 (row方向)，shape (row,)，如 (8,)
            t2: 时间索引 (col方向)，shape (col,)，如 (8,)
        输出：
            sema_params: 优化后的参数，shape (num_peaks * 2,)，如 (4,)
            est1: 重构信号，shape (row, col)，如 (8, 8)
        """
        # 检查输入维度
        assert len(measured_data) == 2 ** self.num_qubits, "measured_data length must be 2^num_qubits"
        assert len(t1) == self.row and len(t2) == self.col, "t1, t2 must match row, col"

        # SEMA 优化
        initial_params = np.random.uniform(0, np.pi, self.num_peaks * 2)
        sema_params = self.optimizer.optimize(self.sema_net, initial_params, measured_data)
        print("SEMA Optimized Parameters:", sema_params)

        # 峰值查找
        peak_circuit = self.peak_net.find_peak()
        backend = Aer.get_backend('qasm_simulator')
        job = execute(peak_circuit, backend, shots=1024)
        result = job.result()
        counts = result.get_counts()
        print("Peak Finder Measurement Counts:", counts)

        # Fisher 信息矩阵
        fisher_info = self.fisher_net.compute_fisher(sema_params)
        print("Fisher Information Matrix:\n", fisher_info)

        # 优化采样网络
        opt_initial_params = np.random.uniform(0, np.pi, self.num_qubits)
        opt_params = self.optimizer.optimize(self.opt_net, opt_initial_params)
        opt_probs = self.opt_net.optimize(opt_params)
        print("Optimized Sampling Probabilities:", opt_probs)

        # 重构信号
        est1 = self.reconstruct_signal(sema_params, t1, t2)
        freq1 = np.fft.fftshift(np.fft.fft2(est1, s=(self.nFFT, self.nFFT)))
        plt.figure()
        plt.imshow(np.abs(freq1), aspect='auto', origin='lower')
        plt.grid(True)
        plt.title('Reconstructed Signal FFT')
        plt.show()

        return sema_params, est1

    def reconstruct_signal(self, params, t1, t2):
        """
        根据优化参数重构信号，类比经典版本的 est1 计算
        """
        est1 = np.zeros((self.row, self.col), dtype=complex)
        for i in range(self.num_peaks):
            # 假设 params = [freq1_x, freq1_y, freq2_x, freq2_y, ...]
            # 这里仅使用频率参数，幅度和阻尼可根据 SEMANetwork 设计扩展
            freq_x = params[i * 2]
            freq_y = params[i * 2 + 1]
            amp = 1.0  # 假设幅度为 1，实际应从优化参数中提取
            term_row = np.exp(2j * np.pi * t1[:, None] * freq_x)
            term_col = np.exp(2j * np.pi * t2[None, :] * freq_y)
            est1 += amp * term_row * term_col
        return est1

if __name__ == "__main__":
    np.random.seed(42)

    # 定义输入数据
    row, col = 8, 8
    t1 = np.arange(row)  # [0, 1, ..., 7]
    t2 = np.arange(col)  # [0, 1, ..., 7]

    # 使用提供的 E_1 数据 (已将 "± i" 替换为 "± ...j")
    E_1 = np.array([
        0.394, 0.144 - 0.144j, 0.144 + 0.144j, 0.105, 0.394, 0.158 - 0.158j, 0.158 + 0.158j, 0.105, 0.394, 0.165 - 0.165j,
        0.165 + 0.165j, 0.105, 0.394, 0.171 - 0.171j, 0.171 + 0.171j, 0.105, 0.394, 0.177 - 0.177j, 0.177 + 0.177j, 0.105,
        0.394, 0.181 - 0.181j, 0.181 + 0.181j, 0.105, 0.394, 0.186 - 0.186j, 0.186 + 0.186j, 0.105, 0.394, 0.190 - 0.190j,
        0.190 + 0.190j, 0.105, 0.394, 0.194 - 0.194j, 0.194 + 0.194j, 0.105, 0.394, 0.197 - 0.197j, 0.197 + 0.197j, 0.105,
        0.394, 0.042 - 0.042j, 0.042 + 0.042j, 0.105, 0.394, 0.031 - 0.031j, 0.031 + 0.031j, 0.105, 0.394, 0.021 - 0.021j,
        0.021 + 0.021j, 0.105, 0.394, 0.010 - 0.010j, 0.010 + 0.010j, 0.105, 0.394, 0.000, 0.000, 0.105,
        0.394, -0.010 + 0.010j, -0.010 - 0.010j, 0.105, 0.394, -0.021 + 0.021j, -0.021 - 0.021j, 0.105, 0.394, -0.031 + 0.031j,
        -0.031 - 0.031j, 0.105, 0.394, -0.042 + 0.042j, -0.042 - 0.042j, 0.105, 0.394, -0.052 + 0.052j, -0.052 - 0.052j, 0.105,
        0.394, 0.194 - 0.194j, 0.194 + 0.194j, 0.105, 0.394, 0.190 - 0.190j, 0.190 + 0.190j, 0.105, 0.394, 0.186 - 0.186j,
        0.186 + 0.186j, 0.105, 0.394, 0.181 - 0.181j, 0.181 + 0.181j, 0.105, 0.394, 0.176 - 0.176j, 0.176 + 0.176j, 0.105,
        0.394, 0.171 - 0.171j, 0.171 + 0.171j, 0.105, 0.394, 0.165 - 0.165j, 0.165 + 0.165j, 0.105, 0.394, 0.158 - 0.158j,
        0.158 + 0.158j, 0.105, 0.394, 0.151 - 0.151j, 0.151 + 0.151j, 0.105, 0.394, -0.144 - 0.144j, -0.144 + 0.144j, 0.105,
        0.394, 0.136 - 0.136j, 0.136 + 0.136j, 0.105, 0.394, 0.128 - 0.128j, 0.128 + 0.128j, 0.105, 0.394, 0.119 - 0.119j,
        0.119 + 0.119j, 0.105, 0.394, 0.111 - 0.111j, 0.111 + 0.111j, 0.105, 0.394, 0.102 - 0.102j, 0.102 + 0.102j, 0.105,
        0.394, 0.092 - 0.092j, 0.092 + 0.092j, 0.105, 0.394, 0.083 - 0.083j, 0.083 + 0.083j, 0.105, 0.394, 0.073 - 0.073j,
        0.073 + 0.073j, 0.105, 0.394, 0.063 - 0.063j, 0.063 + 0.063j, 0.105, 0.394, 0.052 - 0.052j, 0.052 + 0.052j, 0.105,
        0.394, 0.042 - 0.042j, 0.042 + 0.042j, 0.105, 0.394, 0.031 - 0.031j, 0.031 + 0.031j, 0.105, 0.394, 0.021 - 0.021j,
        0.021 + 0.021j, 0.105, 0.394, 0.010 - 0.010j, 0.010 + 0.010j, 0.105, 0.394, 0.000, 0.000, 0.105,
        0.394, -0.010 + 0.010j, -0.010 - 0.010j, 0.105, 0.394, -0.021 + 0.021j, -0.021 - 0.021j, 0.105, 0.394, -0.031 + 0.031j,
        -0.031 - 0.031j, 0.105, 0.394, -0.042 + 0.042j, -0.042 - 0.042j, 0.105, 0.394, -0.052 + 0.052j, -0.052 - 0.052j, 0.105,
        0.394, -0.237 - 0.237j, -0.237 + 0.237j, 0.105, 0.394, -0.247 - 0.247j, -0.247 + 0.247j, 0.105, 0.394, -0.247 - 0.247j,
        -0.247 + 0.247j, 0.105, 0.394, -0.246 - 0.246j, -0.246 + 0.246j, 0.105, 0.394, -0.241 - 0.241j, -0.241 + 0.241j, 0.105,
        0.394, -0.236 - 0.236j, -0.236 + 0.236j, 0.105, 0.394, -0.231 - 0.231j, -0.231 + 0.231j, 0.105, 0.394, -0.226 - 0.226j,
        -0.226 + 0.226j, 0.105, 0.394, -0.220 - 0.220j, -0.220 + 0.220j, 0.105, 0.394, -0.215 - 0.215j, -0.215 + 0.215j, 0.105,
        0.394, -0.249 - 0.249j, -0.249 + 0.249j, 0.105, 0.394, -0.247 - 0.247j, -0.247 + 0.247j, 0.105, 0.394, -0.244 - 0.244j,
        -0.244 + 0.244j, 0.105, 0.394, -0.240 - 0.240j, -0.240 + 0.240j, 0.105, 0.394, -0.236 - 0.236j, -0.236 + 0.236j, 0.105,
        0.394, -0.231 - 0.231j, -0.231 + 0.231j, 0.105, 0.394, -0.226 - 0.226j, -0.226 + 0.226j, 0.105, 0.394, -0.220 - 0.220j,
        -0.220 + 0.220j, 0.105, 0.394, -0.215 - 0.215j, -0.215 + 0.215j, 0.105, 0.394, -0.210 - 0.210j, -0.210 + 0.210j, 0.105,
        0.394, -0.125 - 0.125j, -0.125 + 0.125j, 0.105, 0.394, -0.119 - 0.119j, -0.119 + 0.119j, 0.105, 0.394, -0.113 - 0.113j,
        -0.113 + 0.113j, 0.105, 0.394, -0.106 - 0.106j, -0.106 + 0.106j, 0.105, 0.394, -0.099 - 0.099j, -0.099 + 0.099j, 0.105,
        0.394, -0.092 - 0.092j, -0.092 + 0.092j, 0.105, 0.394, -0.084 - 0.084j, -0.084 + 0.084j, 0.105, 0.394, -0.077 - 0.077j,
        -0.077 + 0.077j, 0.105, 0.394, -0.069 - 0.069j, -0.069 + 0.069j, 0.105, 0.394, -0.061 - 0.061j, -0.061 + 0.061j, 0.105,
        0.394, -0.227 - 0.227j, -0.227 + 0.227j, 0.105, 0.394, -0.221 - 0.221j, -0.221 + 0.221j, 0.105, 0.394, -0.215 - 0.215j,
        -0.215 + 0.215j, 0.105, 0.394, -0.208 - 0.208j, -0.208 + 0.208j, 0.105, 0.394, -0.201 - 0.201j, -0.201 + 0.201j, 0.105,
        0.394, -0.194 - 0.194j, -0.194 + 0.194j, 0.105, 0.394, -0.186 - 0.186j, -0.186 + 0.186j, 0.105, 0.394, -0.179 - 0.179j,
        -0.179 + 0.179j, 0.105, 0.394, -0.172 - 0.172j, -0.172 + 0.172j, 0.105, 0.394, -0.165 - 0.165j, -0.165 + 0.165j, 0.105,
        0.394, -0.236 - 0.236j, -0.236 + 0.236j, 0.105, 0.394, -0.231 - 0.231j, -0.231 + 0.231j, 0.105, 0.394, -0.226 - 0.226j,
        -0.226 + 0.226j, 0.105, 0.394, -0.221 - 0.221j, -0.221 + 0.221j, 0.105, 0.394, -0.215 - 0.215j, -0.215 + 0.215j, 0.105,
        0.394, -0.209 - 0.209j, -0.209 + 0.209j, 0.105, 0.394, -0.203 - 0.203j, -0.203 + 0.203j, 0.105, 0.394, -0.197 - 0.197j,
        -0.197 + 0.197j, 0.105, 0.394, -0.191 - 0.191j, -0.191 + 0.191j, 0.105, 0.394, -0.184 - 0.184j, -0.184 + 0.184j, 0.105,
        0.394, 0.144 - 0.144j, 0.144 + 0.144j, 0.105, 0.394, 0.158 - 0.158j, 0.158 + 0.158j, 0.105, 0.394, 0.165 - 0.165j,
        0.165 + 0.165j, 0.105, 0.394, 0.171 - 0.171j, 0.171 + 0.171j, 0.105, 0.394, 0.177 - 0.177j, 0.177 + 0.177j, 0.105,
        0.394, 0.181 - 0.181j, 0.181 + 0.181j, 0.105, 0.394, 0.186 - 0.186j, 0.186 + 0.186j, 0.105, 0.394, 0.190 - 0.190j,
        0.190 + 0.190j, 0.105, 0.394, 0.194 - 0.194j, 0.194 + 0.194j, 0.105, 0.394, 0.197 - 0.197j, 0.197 + 0.197j, 0.105,
        0.394, 0.217 - 0.217j, 0.217 + 0.217j, 0.105, 0.394, 0.220 - 0.220j, 0.220 + 0.220j, 0.105, 0.394, 0.223 - 0.223j,
        0.223 + 0.223j, 0.105, 0.394, 0.226 - 0.226j, 0.226 + 0.226j, 0.105, 0.394, 0.229 - 0.229j, 0.229 + 0.229j, 0.105,
        0.394, 0.232 - 0.232j, 0.232 + 0.232j, 0.105, 0.394, 0.235 - 0.235j, 0.235 + 0.235j, 0.105, 0.394, 0.238 - 0.238j,
        0.238 + 0.238j, 0.105, 0.394, 0.241 - 0.241j, 0.241 + 0.241j, 0.105, 0.394, 0.244 - 0.244j, 0.244 + 0.244j, 0.105,
        0.394, 0.249, 0.249, 0.105
    ])

    # 接下来与原逻辑一致：
    measured_data = np.abs(E_1) ** 2
    measured_data = np.pad(measured_data, (0, 64 - len(measured_data)), 'constant')
    measured_data /= np.sum(measured_data)
    print("Measured Data (normalized):", measured_data)

    # 运行
    qed = QuantumExperimentalData2D(row=row, col=col)
    sema_params, est1 = qed.run(measured_data, t1, t2)
