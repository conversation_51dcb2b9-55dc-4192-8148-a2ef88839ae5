

# 文件：vqa/povm_xyz_demo.py
import os
import numpy as np
import matplotlib.pyplot as plt

from qiskit import Aer, transpile, QuantumRegister, ClassicalRegister, QuantumCircuit
from qiskit.quantum_info import DensityMatrix, Operator, partial_trace
from qiskit.visualization import plot_state_city

# 导入你第一部分的核心网络与测量
from vqa.networks import PotentialNetwork, Ansatz  # 如果需要别的Network也可
from vqa.QM.statevector import Potential
from vqa.measurement.measurehandler import MeasureHandler
from vqa.measurement.povm_optimization import create_optimized_povm_measure

# 你在 measurement/ancilla.py 里可能已经有 measure_sigma_by_povm 等函数
# from vqa.measurement.ancilla import measure_sigma_by_povm  # 看需求

# 如果需要日志或者别的IO也可:
# from vqa.IO.logger import get_logger


def build_potential_vqa_circuit(ip_number=2, omega=1.0):
    """
    示例函数：基于 PotentialNetwork 构造一条含势能项的量子电路。
    并行演示如何把 Ansatz 叠加到上面（如需要的话）。
    """

    # （1）定义势能对象：例如 Harmonic oscillator
    potential_dict = {
        "HARMONIC": {"OMEGA": omega}
    }

    # （2）构造 PotentialNetwork
    #   这里利用 networks.py 里的 PotentialNetwork.construct(...)
    #   也可以不使用 .construct，直接手动 init
    net = PotentialNetwork.construct(
        num_ip=ip_number,
        hadamard=False,     # 是否在外面包H门可自行决定
        ansatz=None,        # 如果你想先加个 SU2 ansatz，也可:
        POTENTIAL=potential_dict
    )

    # 注意：PotentialNetwork 本身就返回了 net.qc 里包含
    # 一些辅助量子比特操作。如果你想在这之上再加 Ansatz:
    #   my_ansatz = Ansatz(num_ip=ip_number, reps=2)
    #   gate = my_ansatz.qc.to_gate(label="MyAnsatz")
    #   net.qc.compose(gate, qubits=..., inplace=True)

    return net


def measure_in_xyz_povm(network, shots=1024):
    """
    演示：对给定 Network（即已经构造好的量子电路）,
    在X/Y/Z三种基上，以POVM方式进行测量，并返回对应的部分密度矩阵。
    
    思路：
      1. 先通过 Qiskit Aer 模拟 “network.qc” 的最终态
      2. 对辅助比特等做 partial_trace，只保留 data-qubits
      3. 分别对 final_rho 做 X基、Y基 的幺正变换（或独立地进行POVM优化）
      4. 可视化
    """

    backend = Aer.get_backend("aer_simulator")
    # 让电路保存最终 statevector
    network.qc.save_statevector()

    # 编译并运行
    transpiled_qc = transpile(network.qc, backend=backend)
    result = backend.run(transpiled_qc, shots=shots).result()
    final_sv = result.get_statevector()
    full_rho = DensityMatrix(final_sv)

    # =========== PART 1: 做 partial_trace，取 data-qubits 的 \rho ============
    #   这里要根据你的 network 里 data qubits 的位置做 list
    #   比如 PotentialNetwork 默认: [ancilla_cp(1), ancilla(num_ip), ip1(num_ip)]
    #   ip_number=2 => ancilla_cp(0), ancilla(2bits?), ip1(2bits?)
    #   具体需查看 networks.py 里PotentialNetwork的__init__.
    #   这里假设 data 是 network.IPs[0] = 2 bits, 其量子比特 index
    #   可能在电路的最后 2bits (取决于你实际的 register 顺序).
    #   在此仅示例：
    total_qubits = network.qc.num_qubits
    data_indices = list(range(total_qubits - network.IPs[0], total_qubits))
    # ancilla_indices 就是剩下的 qubits
    ancilla_indices = [q for q in range(total_qubits) if q not in data_indices]

    data_rho = partial_trace(full_rho, ancilla_indices)

    # =========== PART 2: 在 X / Y / Z 基下做测量 ============

    # 2.1 先示例 “幺正变换”方法:  
    #     Ux = H, Uy = H*Sdg, Uz=I   (对每个量子比特都做)
    #   (也可以使用 POVM 优化：见 measurement/povm_optimization.py 里create_optimized_povm_measure)

    # 构造 X基变换: H^(⊗ ip_number)
    from qiskit.circuit.library import HGate, SdgGate, IGate
    H = HGate().to_matrix()
    I = IGate().to_matrix()
    Sdg = SdgGate().to_matrix()

    # 2比特示例
    Ux = np.kron(H, H)
    Uy = np.kron(H @ Sdg, H @ Sdg)
    Uz = np.kron(I, I)  # 其实就是单位

    # 变成 Operator
    from qiskit.quantum_info import Operator
    U_X = Operator(Ux)
    U_Y = Operator(Uy)
    U_Z = Operator(Uz)

    # 分别得到 X/Y/Z 基的 \rho
    rho_z = data_rho  # 原本
    rho_x = U_X @ data_rho @ U_X.adjoint()
    rho_y = U_Y @ data_rho @ U_Y.adjoint()

    # =========== PART 3: 以POVM进行测量(可选) ============
    #   如果要对每个基下再用 “povm优化” 来估计 \langle \sigma_z \rangle，做如下:
    #   measure = create_optimized_povm_measure(qc=..., shots=shots)
    #   sigma = measure(network, shots)
    #   ...
    #   这里就看你想要怎样在3个基测量对应的期望。下例仅演示可视化 \rho_x, \rho_y, \rho_z:

    # =========== PART 4: 可视化并返回 ============
    os.makedirs("demo_xyz_povm_plots", exist_ok=True)

    # 绘制Z基
    fig_z = plot_state_city(rho_z, title="Data Qubits in Z Basis")
    fig_z.savefig("demo_xyz_povm_plots/final_state_Z.png")
    plt.close(fig_z)
    # 绘制X基
    fig_x = plot_state_city(rho_x, title="Data Qubits in X Basis")
    fig_x.savefig("demo_xyz_povm_plots/final_state_X.png")
    plt.close(fig_x)
    # 绘制Y基
    fig_y = plot_state_city(rho_y, title="Data Qubits in Y Basis")
    fig_y.savefig("demo_xyz_povm_plots/final_state_Y.png")
    plt.close(fig_y)

    return rho_x, rho_y, rho_z


def main():
    # 1. 构造含势能的Network, ip_number=2, omega=1.0
    net = build_potential_vqa_circuit(ip_number=2, omega=1.0)

    # 2. 在 X/Y/Z 进行测量(含POVM)
    rho_x, rho_y, rho_z = measure_in_xyz_povm(net, shots=1024)

    # 你可以在此对 rho_x/rho_y/rho_z 做进一步处理、或拼接出combined
    # ...

    # 打印一些信息
    print("Measurement in X/Y/Z basis done. See 'demo_xyz_povm_plots/' for images.")


if __name__ == "__main__":
    main()
