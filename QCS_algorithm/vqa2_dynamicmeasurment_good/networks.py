from __future__ import annotations

import uuid

import matplotlib.pyplot as plt
import numpy as np
import datetime as dt

import scipy.linalg

from QM.statevector import Statevector, Potential
from QM.operator import Operator
from copy import deepcopy
from qiskit import QuantumRegister, ClassicalRegister, QuantumCircuit, circuit
from ansatz import SU2
from qiskit import Aer, transpile
from typing import Union
from pathlib import PurePath
from pathlib import Path
from mpslibrary import construct_mpd_circuit
from IO import get_logger
from measurement import MeasureHandler
from tools.tools_depr import compute_generator_expectation_values

# simulator = Aer.get_backend("aer_simulator_statevector") # Change 17.04
simulator = Aer.get_backend("aer_simulator")
FAIL = False
path = Path(__file__).parent.absolute()


logger = get_logger(__name__)


def initialise_registers_kinetic(main_number, anc_reset: bool = True):
    """
    This function initiales a quantum circuit with kinetic type structure,
    that means n main qubits, n-2 ancilla qubits as part of the QNPU and
    1 outer ancilla qubit
    Args:
        main_number: Number of qubits of Input Port
        anc_reset: If true, the n-2 ancilla qubit get
                   reset to |0>

    Returns:
        QuantumCircuit, List[Registers]
    """
    ancilla_cp = QuantumRegister(1, "ancCP")
    ancilla = QuantumRegister(main_number - 2, "ancQNPU")
    ip1 = QuantumRegister(main_number, "IP1")
    registers = [ancilla_cp, ancilla, ip1]
    qc = QuantumCircuit(*registers)
    qc.barrier()
    if anc_reset:
        for anc in ancilla:
            qc.reset(anc)
        for anc in ancilla_cp:
            qc.reset(anc)
    return qc, [ancilla_cp, ancilla, ip1]  # , cr]


def initialise_registers_potential(main_number, anc_reset: bool = True):
    ancilla_cp = QuantumRegister(1, "ancCP")
    ancilla = QuantumRegister(main_number, "ancQNPU")
    ip1 = QuantumRegister(main_number, "IP1")
    # cr = ClassicalRegister(1, "classical")
    registers = [ancilla_cp, ancilla, ip1]  # , cr]
    qc = QuantumCircuit(*registers)
    qc.barrier()
    if anc_reset:
        for anc in ancilla:
            qc.reset(anc)
        for anc in ancilla_cp:
            qc.reset(anc)
    return qc, [ancilla_cp, ancilla, ip1]  # , cr]


def initialise_registers_nonlinear_fake(main_number):
    ip1 = QuantumRegister(main_number, "IP1")
    registers = [ip1]  # , cr]
    qc = QuantumCircuit(*registers)
    qc.barrier()
    return qc, [ip1]  # , cr]


def initialise_registers_potential_qnpu(ip_number):
    anc = QuantumRegister(ip_number, name="ancillaQNPU")
    ip1 = QuantumRegister(ip_number, name="IP1")
    registers = [anc, ip1]
    qc = QuantumCircuit(*registers, name="QNPU V^ Gate")
    return qc, registers


def initialise_registers_non_linear(main_number, control_active: bool = True):
    ancilla_cp = QuantumRegister(1, "ancillaCP")
    ip1 = QuantumRegister(main_number, "IP1")
    ip2 = QuantumRegister(main_number, "IP2")
    ip3 = QuantumRegister(main_number, "IP3")
    cr = ClassicalRegister(1, "classical")
    registers = [ancilla_cp, ip1, ip2, ip3, cr]
    qc = QuantumCircuit(*registers)
    qc.barrier()
    if control_active:
        qc.h(ancilla_cp[0])
    return qc, [ancilla_cp, ip1, ip2, ip3, cr]


def append_inner_scheme_adder(qc, registers, main_number):
    layers = []
    ancilla, ip1 = registers[1:]  #  cr
    for i in range(2, main_number - 2):
        layers.append([i, i - 1])
    for layer in layers:
        qc.cx(ancilla[layer[1]], ip1[layer[0]])
        qc.ccx(ancilla[layer[1]], ip1[layer[0]], ancilla[layer[0]])


def append_end_scheme_adder(qc, registers, ip_number):
    ancilla_cp, ancilla, ip1 = registers  # , cr
    for i in range(1, ip_number - 2).__reversed__():
        qc.ccx(ancilla[i - 1], ip1[i], ancilla[i])
    qc.ccx(ancilla_cp[0], ip1[0], ancilla[0])


def append_adder_network(qc, registers, main_number, control_active: bool = True):
    """
    This function appends the controlled adder network to a kinetic type structure,
    that means n main qubits, n-2 ancilla qubits as part of the QNPU and 1 outer ancilla
    qubit and applies a Hadamard gate onto the outer ancilla, if control_active is True
    Args:
        qc: QuantumCircuit
        registers: List of Registers
        main_number: Number of qubits of the Input Port
        control_active: If true, apply Hadamard to outer
                        ancilla after c-Adder

    Returns:
        QuantumCircuit
    """
    ancilla_cp, ancilla, ip1 = registers  # , class_reg
    qc.cx(ancilla_cp[0], ip1[0])
    qc.ccx(ancilla_cp[0], ip1[0], ip1[1])
    qc.ccx(ancilla_cp[0], ip1[0], ancilla[0])
    qc.ccx(ip1[1], ancilla[0], ancilla[1])
    append_inner_scheme_adder(qc, registers, main_number)
    qc.cx(ancilla[-1], ip1[-2])
    qc.ccx(ancilla[-1], ip1[-2], ip1[-1])
    append_end_scheme_adder(qc, registers, main_number)
    return qc


def append_inner_scheme_adder_modular(qc, registers, num_ip):
    layers = []
    ancilla_reg, ip_reg = registers
    for i in range(2, num_ip - 2):
        layers.append([i, i - 1])

    for layer in layers:
        qc.cx(ancilla_reg[layer[1]], ip_reg[layer[0]])
        qc.ccx(ancilla_reg[layer[1]], ip_reg[layer[0]], ancilla_reg[layer[0]])


def append_end_scheme_adder_modular(qc, registers, num_ip):
    ancilla_reg, ip_reg = registers
    for i in range(1, num_ip - 2).__reversed__():
        qc.ccx(ancilla_reg[i - 1], ip_reg[i], ancilla_reg[i])
    qc.cx(ip_reg[0], ancilla_reg[0])


def build_adder_gate(num_ip, control=1):
    ancilla_reg = QuantumRegister(num_ip - 2, "ancQNPU")
    ip_reg = QuantumRegister(num_ip, "IP")
    registers = [ancilla_reg, ip_reg]
    qc = QuantumCircuit(*registers, name="Adder")
    qc.x(ip_reg[0])
    qc.cx(ip_reg[0], ip_reg[1])
    qc.cx(ip_reg[0], ancilla_reg[0])
    qc.ccx(ip_reg[1], ancilla_reg[0], ancilla_reg[1])
    append_inner_scheme_adder_modular(qc, registers, num_ip)
    qc.cx(ancilla_reg[-1], ip_reg[-2])
    qc.ccx(ancilla_reg[-1], ip_reg[-2], ip_reg[-1])
    append_end_scheme_adder_modular(qc, registers, num_ip)
    # print(qc)
    adder_gate = qc.to_gate().control(control)
    return adder_gate


def append_kinetic_network(nw_ansatz):
    nw_kinetic = Network.create_kinetic_network(nw_ansatz.IPs[0])
    nw = Network.add_networks(nw_ansatz, nw_kinetic, name="ansatz_with_kinetic_basic")
    return nw




class Network:
    """A class to represent a quantum network, it is getting subclassed
    by KineticNetwork, PotentialNetwork and NonlinearNetwork. It comprises
    the quantum circuit, the respective registers, the sizes of different
    Input Ports, the sizes of different ancilla sets and a dictionary for
    the parameter vector(s). When a quantum circuit is parametrised, these
    parameters are stored in an accompanying parameter vector. Initialising
    a Network with a parametrised quantum circuit, the parameter vector
    has to be stored into the 'the param_vectors_dict'. Thus, it is possible
    to assign / bind the parameters using the 'param_vectors_dict'. Multiple
    parameter vectors are also supported by the nature of a dictionary.
    """

    def __init__(
        self,
        qc,
        registers=None,
        ips=None,
        ancillas=None,
        param_vectors_dict: dict = {},
        name: str = uuid.uuid4(),
        type=None,
    ):
        self.qc = qc
        self.qc_assigned = None
        self.save_statevector = False
        self.registers = registers
        self.IPs = ips
        self.ancillas = ancillas
        if self.IPs:
            self.total_size = sum(self.ancillas + self.IPs)
        self.name = name
        self.pmv_dict = param_vectors_dict
        self.type = type
        self.measure_handler = MeasureHandler()
        self.meas_index = 0

    def __repr__(self):
        return f"--Network '{self.name}', #IP-qubits={self.IPs[0]}, #all-qubits={self.total_size}--"

    def get_values_params(self):
        if self.pmv_dict == {}:
            raise Exception("No parameters supplied!")
        else:
            return [value for value in self.pvm_dict[0]["values"]]

    def add_register(
        self, register: Union[QuantumRegister, QuantumCircuit, ClassicalRegister]
    ):
        self.qc.add_register(register)
        self.registers.append(register)

    def get_num_parameters(self):
        return self.qc.num_parameters

    def draw(self, folder: str = None, suffix: str = "", decompose: bool = False):
        filename = (
            PurePath(folder, f"{dt.date.today()}_{suffix}_dec_{decompose}.png")
            if folder
            else PurePath(
                path, "plots", f"{dt.date.today()}_{self.name}_dec_{decompose}.png"
            )
        )
        filename_trans = PurePath(str(filename)[:-4] + "_trans.png")

        if decompose:
            self.qc.decompose().draw(
                output="mpl", style={"fontsize": 22}, filename=filename
            )
        else:
            plt.figure(figsize=(14, 10))
            self.qc.draw(output="mpl", style={"fontsize": 22}, filename=filename)
            plt.tight_layout()
            plt.savefig(filename_trans, transparent=True, dpi=600)

    def draw_assigned(
        self, folder: str = None, suffix: str = "", decompose: bool = False
    ):
        filename = (
            PurePath(folder, f"{dt.date.today()}_{suffix}_dec_{decompose}.png")
            if folder
            else PurePath(
                path, "plots", f"{dt.date.today()}_{self.name}_dec_{decompose}.png"
            )
        )
        self.qc_assigned.decompose().draw(
            output="mpl", style={"backgroundcolor": "#EEEEEE"}, filename=filename
        )

    def set_measure_index(self, val):
        self.meas_index = val

    def increment_measure_index(self):
        self.meas_index += 1

    def assign_parameters(self, param_values, index: int = 0):
        self.qc_assigned = self.qc.assign_parameters(
            {self.pmv_dict[index]["ParamVector"]: param_values}
        )
        self.pmv_dict[index]["values"] = param_values

    def simulate(self):
        # TODO: What happens now when I removed the default ClassicalRegister? This simulate will fail
        if self.qc_assigned:
            t_circ = transpile(self.qc_assigned, simulator)
        else:
            t_circ = transpile(self.qc, simulator)
        result = simulator.run(t_circ).result()
        return result, t_circ

    def estimate_sigma(self, shots: int = 1024):
        sigma = self.measure_handler.estimate_sigma_ancilla(self, shots)
        return sigma

    def activate_save_statevector(self):
        self.qc.save_statevector()
        self.save_statevector = True

    def simulate_state_vector(self):
        if not self.save_statevector:
            raise ValueError("Statevector is not saved at end of circuit")

        if self.qc_assigned:
            t_circ = transpile(self.qc_assigned, simulator)
        else:
            t_circ = transpile(self.qc, simulator)
        result = simulator.run(t_circ).result()
        statevector = Statevector(result.get_statevector(t_circ))
        return statevector

    @classmethod
    def create_non_linear_network(cls, ip_number, control_active: bool = True):
        qc, registers = initialise_registers_non_linear(
            ip_number, control_active=control_active
        )
        return Network(qc, registers, [ip_number, ip_number, ip_number], [1])





class Ansatz(Network):    # 定义继承network类。可以使用network中的属性和方法
    def __init__(
        self,
        num_ip,
        reps,
        layer_scheme="YC",    #描述层的结构类型
        ansatz_type="SU2",    # 类型设置为su2,决定使用何种构造方案。
        include_barriers=False,   # 是否在量子层中加入障碍，可以帮助调试和查看电路结构默认值为false
        entanglement="circular",   # 量子比特的纠缠类型 circular表示循环结构纠缠
    ):
        if ansatz_type == "SU2":     # 创建su2电路
            ansatz_su2 = SU2(         # 使用su2类，创建了一个ansatz_su2的实例
                num_ip, reps, layer_scheme, include_barriers, entanglement   # 传递参数num_ip, reps, layer_scheme, include_barriers, entanglement 
            )  # Set entanglement     # 这个 ansatz_su2 变量会作为ansatz类的基础电路结构。
               # 参数向量和参数字典
            param_vector = ansatz_su2.param_vector  # param_vector：获取ansat_su2的参数向量，用于后续对参数的优化
            pmv_dict = {                          # 创建了一个参数向量字典，里边包含一个索引0，表示初始参数配置
                0: {
                    "ParamVector": param_vector,    # ParamVector：参数向量
                    "values": np.ones(ansatz_su2.num_parameters),   #  values：参数的初始值。所有值初始化为1.
                }
            }
            super().__init__(        # 调用父类（network）的构造函数。
                ansatz_su2.qc,      # 量子电路对象，
                ansatz_su2.registers,  # 量子寄存器
                [num_ip],     #输入的量子比特数量
                [],       # 表示没有额外的输出量子比特。
                param_vectors_dict=pmv_dict,  #参数向量的字典，提供了参数配置
                name="SU2 Ansatz Network",    # 设置电路的名称为SU2 Ansatz Network
            )     
               # 其他配置
            self.save_statevector = False      # 设置是否保存状态向量，当前为false，再调试和测试时比较有用。
            self.natgrad_blocks = ansatz_su2.natgrad_blocks   #继承了ansatz_su2的自然梯度块（natgrad_blocks），用于后续优化中的自然梯度计算。
            self.num_layers = ansatz_su2.num_layers       # 保存了层的数量，表示该ansatz中有多少层
    # 332-364行代码，这段代码主要功能通过传递参数构建一个量子电路ansatz对象。它使用了一个基于su2类的电路实例，随后又调用了父类的构造函数来完成初始化，
    # 并且为电路添加了一些额外的属性（比如参数向量字典； 自然梯度块）

    def embed(self, term):   # 定义了一个embed方法。它主要用于嵌入不同类型的寄存器
        ip_number = self.IPs[0]   # 从self.IPs中取出第一个值作为量子比特的数量。ip_number决定了寄存器的大小。
         # 针对k的嵌入
        if term == "K":       # 如果term是k，则执行以下代码。
            qc, registers = initialise_registers_kinetic(ip_number)  #调用initialise_registers_kinetic函数初始化一个“动能”的量子电路和寄存器
            qc.compose(         # qc.compose():将当前对象的量子电路self.qc组合（合并）到刚才创建的qc中
                self.qc, qubits=range(ip_number - 1, 2 * ip_number - 1), inplace=True  
             # qubits=range(ip_number - 1, 2 * ip_number - 1)：表示合并到量子比特索引范围。inplace=True：表示组合结果是直接应用到qc中，不再创建新的对象。
            )
            # 调用父类构造函数，初始化新嵌入的电路
            super().__init__( 
                qc,       # 使用刚才组合后的量子电路
                registers,  # 使用初始化后的寄存器
                [ip_number],  # 表示输入寄存器的数量
                [1, ip_number - 2],   # 表示输出寄存器的数量和索引范围
                param_vectors_dict=self.pmv_dict,  # 使用类中的参数向量字典elf.pmv_dict
                name=f"ansatz_on_{term}_network",   # 为嵌入的网络设置名称，名称中包括term值。
            )
         
         # 针对v的嵌入
        elif term == "V":   # 如果term是v，表示要嵌入势能寄存器
            qc, registers = initialise_registers_potential(ip_number)  #调用initialise_registers_potential函数初始化一个“势能”的量子电路和寄存器
            qc.compose(     # qc.compose():将当前对象的量子电路self.qc组合（合并）到刚才创建的qc中
                self.qc, qubits=range(ip_number + 1, 2 * ip_number + 1), inplace=True
            # qubits=range(ip_number + 1, 2 * ip_number + 1)：表示不同的量子比特索引范围。inplace=True：表示组合结果是直接应用到qc中，不再创建新的对象。
            )
            super().__init__(
                qc,
                registers,
                [ip_number],
                [1, ip_number],
                param_vectors_dict=self.pmv_dict,
                name=f"ansatz_on_{term}_network",  # 表示该电路是针对v进行的嵌入
            )
            # 针对i的嵌入
        elif term == "I":
            qc, registers = initialise_registers_nonlinear_fake(ip_number)
            qc.compose(self.qc, qubits=range(0, ip_number), inplace=True)
            super().__init__(
                qc,
                registers,
                [ip_number],
                [0, 0],   # 表示输出寄存器的数量和索引的范围均为0，表示该网络仅用作中间处理。
                param_vectors_dict=self.pmv_dict,
                name=f"ansatz_on_{term}_network",
            )

    def construct_blockdiag_fubini_study_metric_tensor(self, params):   # 整体用作于构造fubini_study度量张量
        shots = int(1e4)     # 设置了测量的次数10000次
        num_ip = self.IPs[0]   # 获取量子比特数量
        gs = []      # 初始化一个空列表gs，它主要用于存储fubini_study度量张量
        # print(params)
        for qc_block in self.natgrad_blocks:    #遍历self.natgrad_blocks中每个块，这些块是梯度计算中用到的量子电路块
            # print(params[: qc_block.num_parameters])
            # params[: qc_block.num_parameters]：指定qc_block的参数为输入的params
            qc_block = qc_block.assign_parameters(
                params[: qc_block.num_parameters], inplace=False     # inplace=False：指的是不在原对象上修改，而是生成了新的量子电路对象
            )
            # print(qc_block)

            logger.debug(qc_block)    # debug用于调试打印qc_block的信息
            counts = MeasureHandler.measure_all(qc_block, shots=shots)  # 通过MeasureHandler对qc_block进行测量，执行shots多少次的测量操作，结果保存在counts中。
            k_i, k_ij = compute_generator_expectation_values(counts, shots) # 使用测量结果counts和测量次数shots，计算生成器的期望值，返回两个值k_i, k_ij
            gs.append(np.array(k_ij - np.outer(k_i, k_i))) # 将计算得到的fubini_study度量张量结果是(k_ij - np.outer(k_i, k_i))以nupy数组的形式添加到列表gs中
                                                           # 这里的计算是为了得到张量形式通过内积的差值来表示
        fsmt = np.real(scipy.linalg.block_diag(*gs))   # 使用scipy的块对角函数，构建fubini_study度量张量块对角矩阵
        # np.testing.assert_array_equal(FSMT, FSMT2)
        return fsmt    # 返回计算得到后的矩阵

    def get_state_vector(self, params):   # 这个函数用于获取当前量子态的向量
        if not self.save_statevector:      # 检查是否需要保存状态向量，如果不是，则激活保存状态向量的选项
            self.activate_save_statevector()  
        self.assign_parameters(params)     # 使用给定的参数作为电路分配参数
        state = self.simulate_state_vector()    # 进行量子态向量的模拟
        return state  # 返回模拟的量子态


class KineticNetwork(Network):
    def __init__(self, qc, registers, num_ip, ansatz=None):
        super().__init__(
            qc,
            registers,
            [num_ip],
            [1, num_ip - 2],
            name="Kinetic Network",
            type="K",
        )
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz
        self.h_n = 2 / (2**num_ip)
        self.rescaling = -1 / (self.h_n**2)
        self.energy = None

    # def prepend_ansatz_state(self, params):
    #     print(params)
    #     state = self.ansatz.get_prepared_state(params)
    #     print(self.qc)
    #     self.qc_assigned, registers = initialise_registers_kinetic(self.IPs[0])
    #     gate = StatePreparation(state.get_amplitudes(), label="Ansatz")
    #     a = self.qc_assigned.compose(gate, qubits=[3, 4, 5, 6])
    #     print(a)

    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):
            self.assign_parameters(params)

        self.set_measure_index(0)
        sigma = self.measure_handler.estimate_sigma_ancilla(self, shots)
        self.energy = (1 - sigma) / (self.h_n**2)
        return self.energy

    def get_nth_gradient_component(self, params, shots, nth):
        params[nth] += np.pi / 2
        self.assign_parameters(params)
        sigma_ps = self.estimate_sigma(shots)
        self.increment_measure_index()

        params[nth] -= np.pi
        self.assign_parameters(params)
        sigma_ns = self.estimate_sigma(shots)
        self.increment_measure_index()

        res = self.rescaling * (sigma_ps - sigma_ns) * 1 / 2
        params[nth] += np.pi / 2
        return res

    @classmethod
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs):
        qc, registers = KineticNetwork.initialise_registers(num_ip, anc_reset=True)
        if ansatz:
            ansatz = deepcopy(ansatz)
            gate = ansatz.qc.to_gate(label="Ansatz")
            qc.compose(gate, qubits=range(num_ip - 1, 2 * num_ip - 1), inplace=True)
            qc.barrier()

        if hadamard:
            qc.h(registers[0][0])
        adder_gate = KineticNetwork.build_adder_gate(num_ip, control=1)
        qc.compose(adder_gate, qubits=range(0, 2 * num_ip - 1), inplace=True)
        if hadamard:
            qc.h(registers[0][0])
        return KineticNetwork(qc, registers, num_ip, ansatz=ansatz)

    @staticmethod
    def initialise_registers(num_ip, anc_reset: bool = True):
        """
        This function initiales a quantum circuit with kinetic type structure,
        that means n main qubits, n-2 ancilla qubits as part of the QNPU and
        1 outer ancilla qubit
        Args:
            num_ip: Number of qubits of Input Port
            anc_reset: If true, the n-2 ancilla qubit get
                       reset to |0>

        Returns:
            QuantumCircuit, List[Registers]
        """
        ancilla_cp = QuantumRegister(1, "ancCP")
        ancilla = QuantumRegister(num_ip - 2, "ancQNPU")
        ip1 = QuantumRegister(num_ip, "IP1")
        registers = [ancilla_cp, ancilla, ip1]
        qc = QuantumCircuit(*registers)
        qc.barrier()
        if anc_reset:
            for anc in ancilla:
                qc.reset(anc)
            for anc in ancilla_cp:
                qc.reset(anc)
        return qc, [ancilla_cp, ancilla, ip1]

    @staticmethod
    def append_inner_scheme_adder(qc, registers, num_ip):
        layers = []
        ancilla_reg, ip_reg = registers
        for i in range(2, num_ip - 2):
            layers.append([i, i - 1])

        for layer in layers:
            qc.cx(ancilla_reg[layer[1]], ip_reg[layer[0]])
            qc.ccx(ancilla_reg[layer[1]], ip_reg[layer[0]], ancilla_reg[layer[0]])

    @staticmethod
    def append_end_scheme_adder(qc, registers, num_ip):
        ancilla_reg, ip_reg = registers
        for i in range(1, num_ip - 2).__reversed__():
            qc.ccx(ancilla_reg[i - 1], ip_reg[i], ancilla_reg[i])
        qc.cx(ip_reg[0], ancilla_reg[0])

    @staticmethod
    def build_adder_gate(num_ip, control=1):
        ancilla_reg = QuantumRegister(num_ip - 2, "ancQNPU")
        ip_reg = QuantumRegister(num_ip, "IP")
        registers = [ancilla_reg, ip_reg]
        qc = QuantumCircuit(*registers, name="Adder")
        qc.x(ip_reg[0])
        qc.cx(ip_reg[0], ip_reg[1])
        qc.cx(ip_reg[0], ancilla_reg[0])
        qc.ccx(ip_reg[1], ancilla_reg[0], ancilla_reg[1])
        KineticNetwork.append_inner_scheme_adder(qc, registers, num_ip)
        qc.cx(ancilla_reg[-1], ip_reg[-2])
        qc.ccx(ancilla_reg[-1], ip_reg[-2], ip_reg[-1])
        KineticNetwork.append_end_scheme_adder(qc, registers, num_ip)
        adder_gate = qc.to_gate().control(control)
        return adder_gate


# ansatz = Ansatz(4, 2, include_barriers=False)
# kin = KineticNetwork.construct(4, ansatz=ansatz)
# print(kin.qc)
# a = np.ones(4 * 3)


class PotentialNetwork(Network): # 定义PotentialNetwork类，继承Network类
     # qc 是量子电路对象，registers：量子寄存器，num_ip：量子比特数量，potential：表示势能的对象，ansatz：表示一个变分量子特征态对象，默认值为None
    def __init__(self, qc, registers, num_ip, potential, ansatz=None):   # 这是PotentialNetwork类的构造函数，用于初始化对象，它由5个参数
        # super().__init__(）：调用父类Network的构造函数，并且传递了一些初始化参数，
        # qc 是量子电路对象，registers：量子寄存器，num_ip：量子比特数量，
        super().__init__(      
            qc,              
            registers,
            [num_ip],
            [1, num_ip],  #用于某些初始化的量子比特的索引范围
            name="PotentialNetwork",    # 指定网络的名称为：PotentialNetwork。这是变分量子特征态。
            type="V",     # 表示势能网络。
        )
        # 595-597的代码： 如果传递了ansatz参数，则将ansatz中的参数字典pmv_dict赋值给当前对象的属性pmv_dict，并且将ansatz保存到对象属性中。
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz

        self.potential = potential    # 将传入的potential赋值给对象属性self.potential，这个属性用于存储势能信息。
        self.rescaling = self.potential.alpha  # 获取potential的alpha属性，存储到self.rescaling中。
        self.energy = None    # 将self.energy初始化为one，方便后续计算当中存储能量值
    # 定义了compute_cost方法，用于计算网络损失函数或代价。它由两个参数：params是一个np.数组，它是表示需要分配的参数。shots：用于量子测量的采样次数，默认值为1024
    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):  # 检查params是否为np的数组或者是列表的类型，如果是则会调用assign_parameters(params)方法，
            self.assign_parameters(params)          # 然后将参数分配给量子电路。用于设置量子电路中可调节的变分量子参数。

        self.set_measure_index(0)       # 调用set_measure_index(0)方法，将测量的索引设为0.用于指定测量的目标，或者是初始化测量的顺序。
        # 通过self.measure_handler.estimate_sigma_ancilla来计算sigma。measure_handler：负责测量的对象, 它这里使用辅助量子比特ancilla进行测量，并返回了
        # 了一个值sigma，代表某种度量或者测量的结果。
        sigma = self.measure_handler.estimate_sigma_ancilla(self, shots)  
        # 将sigma值乘以self.rescaling，并且将结果存储到self.energy。rescaling:缩放因子，用于对测量结果sigma概率统计一个最终的能量经过缩放后的测量值。
        self.energy = self.rescaling * sigma  
        return self.energy     # 返回计算得到的当前状态下的self.energy能量值。能量                 
    
    # 定义方法get_nth_gradient_component，用于计算给定参数的第nth个分量。params：参数向量，表示要调整的量子电路参数，
    # shots：测量次数，它是表示量子计算中为了获得可靠结果所进行的重复次数。nth：指定要计算的梯度的参数索引。
    def get_nth_gradient_component(self, params, shots, nth):  
        params[nth] += np.pi / 2  # 对params中的索引为nth的参数增加pi/2。
        self.assign_parameters(params)    # 将更新后的参数分配到量子电路中，通过调整参数使量子电路的状态进行变化，从而进行后续测量。
        # 使用当前参数配置来执行量子测量，获取测量值sigma_ps，ps：正向偏移。estimate_sigma：这个方法进行多次的量子测量返回结果。
        sigma_ps = self.estimate_sigma(shots)  
        self.increment_measure_index()  # 增加测量的索引，主要用于记录或者是标记当前测量步骤。

        params[nth] -= np.pi     # 将params中的索引为nth的参数减少pi。此时，相当于在初始的位置的基础上减去pi/2，用于梯度的负方向。
        self.assign_parameters(params)   # 再次将调整后的参数，再将更新后的参数分配到量子电路中，通过调整参数使量子电路的状态进行变化，从而进行后续测量。
        sigma_ns = self.estimate_sigma(shots)   # 使用当前参数执行测量，获得测量值sigma_ns。ns：负方向测量
        self.increment_measure_index()   # 再次增加测量索引，继续跟踪测量的步骤。

        # 根据量子测量结果计算梯度值。sigma_ps - sigma_ns：是基于差分方法估计梯度，乘以1/2进行归一化。self.rescaling：是缩放因子，对结果进行适当调整。
        res = self.rescaling * (sigma_ps - sigma_ns) * 1 / 2  
        params[nth] += np.pi / 2   # ↩恢复params中的第nth个参数的值，使它回到初始状态，这是为了不影响后续的梯度计算或者是其他操作
        return res  # 返回计算后的梯度值res

    @classmethod     # 类方法：它的意思通过类本身调用不需要实例化，
        # 定义类方法construct，参数包括：输入的量子比特数num_ip，是否加入hadamard门布尔值，量子态准备网络ansatz和任意数量的其他参数**kwargs
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs): 
        # 用于输入量子比特数num_ip和字典potential_dict，它会来创建一个potential对象
        potential = Potential(num_ip=num_ip, potential_dict=kwargs["POTENTIAL"])

        if potential.off:   # 检查potential对象是否并禁用
            # 如果这个potential被禁用则返回一个由QuantumCircuit(1)构建一个空的量子网络，
            return PotentialNetwork(qc=QuantumCircuit(1),  registers=[], num_ip=1, potential=potential)
        
        # 它会调用这个PotentialNetwork类方法initialise_registers初始化量子电路和寄存器。输入量子比特数为um_ip并设置辅助量子比特为重置状态。
        qc, registers = PotentialNetwork.initialise_registers(num_ip, anc_reset=True)
        if ansatz:    # 如果提供了ansatz参数，
            ansatz = deepcopy(ansatz)    # 对ansatz进行深拷贝，防止修改原始对象，
            gate = ansatz.qc.to_gate(label="Ansatz")    # 将ansatz对象中的量子电路qc转化为一个量子门，标记为Ansatz
            qc.compose(gate, qubits=range(num_ip+1, 2 * num_ip + 1), inplace=True)  # 它会将生成的量子门gate添加到当前的量子电路qc上，用于指定量子比特
            qc.barrier()     # 在电路中添加屏障，防止量子门被修改。

        if hadamard:      # 如果这个adamard参数为True真，则应用hadamard门
            qc.h(registers[0][0])   # 在第一个寄存器的第一个量子比特上应用hadamard门

        # a_ks = [0, 0, 1 / 2 * omega**2]
        # potential_gate, alpha, potential = PotentialNetwork.build_harmonic_osc_gate(
        #     num_ip, a_ks=a_ks, num_control=1
        # )
        # 调用这个类PotentialNetwork类方法build_potential_gate，创建一个与势能相关的量子门，输入参数量子比特数量num_ip，势能对象potential，
        # num_control=1：指定控制量子比特的数量。
        potential_gate = PotentialNetwork.build_potential_gate(num_ip, potential, num_control=1)

        # 将potential_gate，它会加入到当前的量子电路中，qc.compose：它是将新的量子门添加到已经有的量子门中，potential_gate：要加入到量子门对象
        # qubits=range(0, 2 * num_ip + 1)：指定量子门的作用，量子比特的一个索引范围，从0到2*num_ip+1，inplace：表示现有的量子电路qc直接修改
        # 不生成新的量子电路。
        qc.compose(potential_gate, qubits=range(0, 2 * num_ip + 1), inplace=True)
        if hadamard:   # 如果这个adamard参数为True真，则应用hadamard门
            qc.h(registers[0][0])   # 在第一个寄存器的第一个量子比特上应用hadamard门
        # 返回了一个PotentialNetwork实例，该实例包含构建好的量子电路qc，
        return PotentialNetwork(
            qc, registers, num_ip, potential, ansatz=ansatz  # qc：完成了组合势能门和可能的hadamard门的量子电路
        )

    @staticmethod # 静态方法，用来构建与势能相关的量子门
    # num_ip：量子比特数量，potential：与势能相关的对象，num_control=1：控制量子比特的数量，默认为1.
    def build_potential_gate(num_ip, potential, num_control=1): 
        # 创建两个量子寄存器。
        anc = QuantumRegister(num_ip, name="ancQNPU")  # 创建了ancQNPU寄存器，它包含num_ip个量子比特，主要是用于辅助函数anc
        ip1 = QuantumRegister(num_ip, name="IP1")  # 创建了IP1寄存器，它包含num_ip个量子比特，表示主要的输入寄存器
        registers = [anc, ip1]  # 将创建的寄存器存储在一个列表中
        # 使用寄存器创建一个量子电路，QuantumCircuit(*registers)：解包寄存器列表，将anc和ip1作为参数传入，然后创建量子电路。给该电路命名为：V^ Gate
        qc = QuantumCircuit(*registers, name="V^ Gate")   
        # qc_mpd, alpha, potential = construct_mpd_circuit(n=num_ip, a_ks=a_ks)
        # circuit.library.StatePreparation：从qiskit中去导入量子态准备的门。potential.get_amplitudes：获取势能的振幅用于初始化状态。
        gate_init = circuit.library.StatePreparation(potential.get_amplitudes()) 
        # 将初始化的状态准备门gate_init组合的量子网络qc中，qc.compose：这个就是将门或者是电路组合到现有的电路中，gate_init：需要组合的量子门。
        # qubits=range(0, num_ip)：指定量子门作用量子比特从0到num_ip。inplace=True：在现有的量子电路中直接进行修改。
        qc.compose(gate_init, qubits=range(0, num_ip), inplace=True)

        # 691-692: 在辅助量子比特和输入量子比特之间应用cnot量子门。
        for qbit_anc, qbit_ip in zip(registers[0], registers[1]): # 将辅助的寄存器和输入的寄存器当中的量子比特配对遍历
            qc.cnot(qbit_ip, qbit_anc)     # 再输入量子比特qbit_ip和辅助量子比特qbit_anc之间应用cnot门
        controlled_v = qc.to_gate().control(num_control)  # 将构建好的量子电路转化为受控门controlled_v，受控量子比特数量是num_control。
        return controlled_v # 返回构建的受控量子门。

    @staticmethod # 静态方法，通过类和别的函数都可以调用
    def build_harmoninc_osc_gate(num_ip, a_ks, num_control=1):  # 定义方法，num_ip：量子比特数量，a_ks：一些参数，num_control=1：用于控制门的数量，默认是1
        # 698-699创建量子寄存器，anc 和ip1是两个量子寄存器，各包含num_ip量子比特数，anc被命名为ancQNPU，ip1被命名为IP1。
        anc = QuantumRegister(num_ip, name="ancQNPU")  
        ip1 = QuantumRegister(num_ip, name="IP1")  
        registers = [anc, ip1]     # 创建量子电路，把两个寄存器组合成一个列表registers
        qc = QuantumCircuit(*registers, name="V^ Gate")   # 使用这些寄存器来构建一个新的量子电路qc，然后给它命名为V^ Gate。
        # 调用了construct_mpd_circuit的参数，来构造一个多参数驱动mpd电路，这个参数返回了qc_mpd量子电路，alpha和potential就是后续相关的参数。
        qc_mpd, alpha, potential = construct_mpd_circuit(n=num_ip, a_ks=a_ks) 
        # 将生产的qc_mpd电路合成到qc，作用在量子比特范围是0到num_ip，inplace=True：表示直接在原量子电路上修改。
        qc.compose(qc_mpd, qubits=range(0, num_ip), inplace=True)  
        # 708-709: 将辅助寄存器和输入寄存器中的量子比特一一配对，并且执行了cnot门的操作。它的控制量子比特为qbit_ip，目标量子比特为qbit_anc
        for qbit_anc, qbit_ip in zip(registers[0], registers[1]):  
            qc.cnot(qbit_ip, qbit_anc)
        controlled_v = qc.to_gate().control(num_control) # 将整个量子电路qc转化为一个门操作，并且为它添加控制量子比特，控制数量为num_control，返回711行
        return controlled_v, alpha, potential   # 返回生产的受控门以及alpha，势能。

    @staticmethod # 静态方法，初始化量子寄存器，
    # 定义一个方法initialise_registers，接受两个参数，一个量子比特数量num_ip，一个anc_reset=True：控制辅助寄存器是否需要重置，默认为True
    def initialise_registers(num_ip, anc_reset=True): 
        # 717-718: 创建辅助量子寄存器，717:创建一个包含一个量子比特的量子寄存器名称为ancilla_cp。718:创建了一个包含num_ip个的量子比特的量子寄存器名称为ancQNPU。
        ancilla_cp = QuantumRegister(1, "ancCP")   
        ancilla = QuantumRegister(num_ip, "ancQNPU")
        ip1 = QuantumRegister(num_ip, "IP1")   # 创建了一个包含num_ip个量子比特的量子寄存器，名称为IP1。
        registers = [ancilla_cp, ancilla, ip1]  # 将所有的寄存器存储到列表registers中。
        qc = QuantumCircuit(*registers)   # 使用registers中所有的寄存器来创建一个量子电路qc。
        qc.barrier()   # 在量子电路总添加一个屏障。它会去用于阻止量子门之间的优化确保量子门执行顺序。
        if anc_reset:    # 如果anc_reset为True的话，执行下边的量子寄存器重置操作。
            for anc in ancilla:  # 遍历辅助寄存器ancilla中的每个量子比特，并且调用qc.reset(anc)来重置它们。
                qc.reset(anc)
            for anc in ancilla_cp:   # 遍历ancilla_cp中的量子比特并重置它们。
                qc.reset(anc)
        return qc, [ancilla_cp, ancilla, ip1]  # 返回了构建好的量子电路qc，以及包含ancilla_cp, ancilla, ip1的寄存器列表。


# ansatz = Ansatz(4, 2, include_barriers=False)
# pot = PotentialNetwork.construct(4, **{"OMEGA_HO": 2}, ansatz=ansatz)
# print(pot.qc)

class NonLinearNetwork(Network):
    def __init__(self, qc, registers, num_ip, g, ansatz=None):
        super().__init__(
            qc,
            registers,
            [num_ip],
            [0, 0],
            name="Non-linear network",
            type="I",
        )
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz

        self.activate_save_statevector()
        self.h_n = 2 / (2**num_ip)
        self.g = g
        self.rescaling = 1 / 2 * self.g / self.h_n
        self.energy = None

    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):
            self.assign_parameters(params)

        self.set_measure_index(0)
        state_current = self.simulate_state_vector()
        op = Operator.create_non_linear_space_rep(state_current)
        exp_value = op.get_expectation_value(state_current)
        self.energy = self.rescaling * exp_value
        return self.energy

    def get_nth_gradient_component(self, params, shots, nth):
        self.assign_parameters(params)
        state_current = self.simulate_state_vector()
        op_current = Operator.create_non_linear_space_rep(state_current)

        params[nth] += np.pi / 2
        self.assign_parameters(params)
        state_pos_shift = self.simulate_state_vector()  # Positive shift
        sigma_pos_shift = op_current.get_expectation_value(state_pos_shift)
        self.increment_measure_index()

        params[nth] -= np.pi
        self.assign_parameters(params)
        state_neg_shift = self.simulate_state_vector()  # Negative shift
        sigma_neg_shift = op_current.get_expectation_value(state_neg_shift)

        params[nth] += 3*np.pi / 2
        # params[nth] += np.pi
        self.assign_parameters(params)
        STATE_PI_SHIFT = self.simulate_state_vector()  # PI SHIFT

        op_ps = Operator.create_non_linear_space_rep_2(state_current, STATE_PI_SHIFT)
        sigma_op_pos_shift = op_ps.get_expectation_value(state_current)
        # logger.info(f"PARAM PI/2 shift: {self.rescaling * (sigma_pos_shift - sigma_neg_shift) * 1 / 2}")
        # logger.info(f"PI POS SHIFT: {self.rescaling * sigma_op_pos_shift}")
        res = self.rescaling * (
            (sigma_pos_shift - sigma_neg_shift) * 1 / 2
            + sigma_op_pos_shift * 2 * 1/2
        )
        params[nth] -= np.pi
        return res

    @classmethod
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs):
        g = kwargs["G"]
        if g == 0:
            return NonLinearNetwork(qc=QuantumCircuit(1), registers=[], num_ip=1, g=g)

        qc, registers = NonLinearNetwork.initialise_registers_fake(num_ip)
        if ansatz:
            ansatz = deepcopy(ansatz)
            gate = ansatz.qc.to_gate(label="Ansatz")
            qc.compose(gate, inplace=True)

        # if hadamard:
        #     qc.h(registers[0][0])

        # NonLinearNetwork.build_gate
        # qc.compose(nonlinear gate, qubits=range(0, 2 * num_ip + 1), inplace=True)
        # if hadamard:
        #     qc.h(registers[0][0])
        return NonLinearNetwork(
            qc, registers, num_ip, g, ansatz=ansatz
        )

    @staticmethod
    def initialise_registers_fake(num_ip):
        ip1 = QuantumRegister(num_ip, "IP1")
        registers = [ip1]
        qc = QuantumCircuit(*registers)
        qc.barrier()
        return qc, [ip1]


class OverlapNetwork(Network):
    def __init__(self, qc, registers, num_ip, ansatz=None):
        super().__init__(
            qc,
            registers,
            [num_ip],
            [0, 0],
            name="Overlap network",
            type="O",
        )
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz

        self.activate_save_statevector()
        f = np.ones(2**num_ip)
        f = f / np.linalg.norm(f)
        self.function = Statevector(f)
        self.h_n = 2 / (2**num_ip)
        self.rescaling = 1 / 10
        self.energy = None

    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):
            self.assign_parameters(params)

        self.set_measure_index(0)
        state_current = self.simulate_state_vector()
        overlap = np.real(state_current.get_overlap_with_target(self.function))
        self.energy = -self.rescaling * overlap # * np.sqrt(self.h_n)

        op = Operator.create_laplace_pbc(self.IPs[0])
        state_current_f = state_current.get_amplitudes() / np.sqrt(self.h_n)
        # print(op.op.shape, state_current.get_amplitudes().shape,)
        diff = op.op @ state_current.get_amplitudes() - self.rescaling * self.function.get_amplitudes()
        print("DIFF", diff)
        print("LAPLACE X F: ", op.op @ state_current.get_amplitudes(), self.function.get_amplitudes(), self.h_n)
        # print(np.dot(op.op, state_current.get_amplitudes()))
        # print(self.function.get_amplitudes())
        # print("ENERGY OPErator:", self.h_n * state_current.get_amplitudes() @ op.op @ state_current.get_amplitudes())
        # print("ZERO??", op.op @ np.ones(2**self.IPs[0]))
        return self.energy

    def get_nth_gradient_component(self, params, shots, nth):
        dtheta = 1e-5
        params[nth] += dtheta
        pos = self.compute_cost(params)
        params[nth] -= 2*dtheta
        neg = self.compute_cost(params)
        grad = (pos - neg) / (2*dtheta)
        params[nth] += dtheta

        params[nth] += np.pi
        self.assign_parameters(params)
        state_current = self.simulate_state_vector()

        grad2 = 1/2 * np.real(state_current.get_overlap_with_target(self.function)) * np.sqrt(self.h_n)
        grad2 = -grad2 * self.rescaling
        params[nth] -= np.pi
        # print(grad, grad2)
        # raise ValueError
        return grad2

    @classmethod
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs):
        # if g == 0:
        #    return NonLinearNetwork(qc=QuantumCircuit(1), registers=[], num_ip=1, g=g)

        qc, registers = OverlapNetwork.initialise_registers_fake(num_ip)
        if ansatz:
            ansatz = deepcopy(ansatz)
            gate = ansatz.qc.to_gate(label="Ansatz")
            qc.compose(gate, inplace=True)

        # if hadamard:
        #     qc.h(registers[0][0])

        # NonLinearNetwork.build_gate
        # qc.compose(nonlinear gate, qubits=range(0, 2 * num_ip + 1), inplace=True)
        # if hadamard:
        #     qc.h(registers[0][0])
        return OverlapNetwork(
            qc, registers, num_ip, ansatz=ansatz
        )

    @staticmethod
    def initialise_registers_fake(num_ip):
        ip1 = QuantumRegister(num_ip, "IP1")
        registers = [ip1]
        qc = QuantumCircuit(*registers)
        qc.barrier()
        return qc, [ip1]


class Laplace(Network):
    def __init__(self, qc, registers, num_ip, ansatz=None):
        super().__init__(
            qc,
            registers,
            [num_ip],
            [1, num_ip - 2],
            name="Laplace Network",
            type="L",
        )
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz
        self.h_n = 2 / (2**num_ip)
        self.rescaling = 2 / (self.h_n**2)
        self.energy = None

    # def prepend_ansatz_state(self, params):
    #     print(params)
    #     state = self.ansatz.get_prepared_state(params)
    #     print(self.qc)
    #     self.qc_assigned, registers = initialise_registers_kinetic(self.IPs[0])
    #     gate = StatePreparation(state.get_amplitudes(), label="Ansatz")
    #     a = self.qc_assigned.compose(gate, qubits=[3, 4, 5, 6])
    #     print(a)

    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):
            self.assign_parameters(params)

        self.set_measure_index(0)
        sigma = self.estimate_sigma(shots)
        self.energy = (-2 + 2 * sigma) / (self.h_n**2)

        state = self.ansatz.get_state_vector(params)
        op = Operator.create_laplace_pbc(self.IPs[0])
        energy = state.get_amplitudes() @ op.op @ state.get_amplitudes()
        print("ENERGY LAPLACE", self.energy, energy)
        return self.energy

    def get_nth_gradient_component(self, params, shots, nth):
        params[nth] += np.pi / 2
        self.assign_parameters(params)
        sigma_ps = self.estimate_sigma(shots)
        self.increment_measure_index()

        params[nth] -= np.pi
        self.assign_parameters(params)
        sigma_ns = self.estimate_sigma(shots)
        self.increment_measure_index()

        res = self.rescaling * (sigma_ps - sigma_ns) * 1 / 2
        params[nth] += np.pi / 2
        return res

    @classmethod
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs):
        qc, registers = KineticNetwork.initialise_registers(num_ip, anc_reset=True)
        if ansatz:
            ansatz = deepcopy(ansatz)
            gate = ansatz.qc.to_gate(label="Ansatz")
            qc.compose(gate, qubits=range(num_ip - 1, 2 * num_ip - 1), inplace=True)
            qc.barrier()

        if hadamard:
            qc.h(registers[0][0])
        adder_gate = KineticNetwork.build_adder_gate(num_ip, control=1)
        qc.compose(adder_gate, qubits=range(0, 2 * num_ip - 1), inplace=True)
        if hadamard:
            qc.h(registers[0][0])
        return Laplace(qc, registers, num_ip, ansatz=ansatz)

    @staticmethod
    def initialise_registers(num_ip, anc_reset: bool = True):
        """
        This function initiales a quantum circuit with kinetic type structure,
        that means n main qubits, n-2 ancilla qubits as part of the QNPU and
        1 outer ancilla qubit
        Args:
            num_ip: Number of qubits of Input Port
            anc_reset: If true, the n-2 ancilla qubit get
                       reset to |0>

        Returns:
            QuantumCircuit, List[Registers]
        """
        ancilla_cp = QuantumRegister(1, "ancCP")
        ancilla = QuantumRegister(num_ip - 2, "ancQNPU")
        ip1 = QuantumRegister(num_ip, "IP1")
        registers = [ancilla_cp, ancilla, ip1]
        qc = QuantumCircuit(*registers)
        qc.barrier()
        if anc_reset:
            for anc in ancilla:
                qc.reset(anc)
            for anc in ancilla_cp:
                qc.reset(anc)
        return qc, [ancilla_cp, ancilla, ip1]


def init_network_kinetic_with_ansatz_su2(ip_number, reps):
    nw = Network.create_ansatz_network_su2(ip_number, term="kin", reps=reps)
    nw_adder = Network.create_kinetic_network(ip_number)
    nw_master = Network.add_networks(nw, nw_adder, name="ansatz_with_kinetic_init")
    return nw_master


def init_network_identity(ip_number, reps):
    nw = Network.create_ansatz_network_su2(ip_number, term="kin", reps=reps)
    nw_adder = Network.create_kinetic_network(ip_number, control_active=False)
    nw_master = Network.add_networks(nw, nw_adder, name="ansatz_with_identity")
    return nw_master


def get_network(term):
    if term.upper() == "K":
        return KineticNetwork
    elif term.upper() == "V":
        return PotentialNetwork
    elif term.upper() == "I":
        return NonLinearNetwork
    elif term.upper() == "O":
        return OverlapNetwork
    elif term.upper() == "L":
        return Laplace
    else:
        raise Exception(f"""No network for term '{term}' known!""")


