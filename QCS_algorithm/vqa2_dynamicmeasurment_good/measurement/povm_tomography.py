
import numpy as np
from qiskit import Quantum<PERSON>ircuit, ClassicalReg<PERSON>, Aer, transpile
from qiskit.providers.aer import AerSimulator
from qiskit.extensions import UnitaryGate

###############################################################################
# SIC-POVM 四个向量定义
###############################################################################
phi0 = np.array([1.0, 0.0], dtype=complex)
phi1 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2)], dtype=complex)
phi2 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 2 * np.pi / 3)], dtype=complex)
phi3 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 4 * np.pi / 3)], dtype=complex)
povm_phis = [phi0, phi1, phi2, phi3]

###############################################################################
# 辅助函数：计算将 phi 映射到 |0> 的酉矩阵
###############################################################################
def _get_unitary_map_phi_to_0(phi):
    """返回 2x2 酉矩阵 U，使得 U|0> = phi"""
    norm = np.linalg.norm(phi)
    phi_n = phi / norm

    a, b = phi_n[0], phi_n[1]
    orth = np.array([-np.conjugate(b), np.conjugate(a)], dtype=complex)
    orth /= np.linalg.norm(orth)
    U = np.column_stack([phi_n, orth])
    return U

###############################################################################
# SIC-POVM 测量概率
###############################################################################
def measure_probabilities_sic_povm(qc_original, shots=1024):
    """
    对单比特电路使用 SIC-POVM 测量，返回概率 [p0, p1, p2, p3]。
    """
    simulator = AerSimulator()
    p_i_list = []

    for i, phi in enumerate(povm_phis):
        qc_meas = qc_original.copy()
        qc_meas = transpile(qc_meas, simulator)

        U_i = _get_unitary_map_phi_to_0(phi)
        U_i_dagger = np.conjugate(U_i.T)
        gate = UnitaryGate(U_i_dagger, label=f"SIC_{i}")
        qc_meas.append(gate, [0])

        creg = ClassicalRegister(1)
        qc_meas.add_register(creg)
        qc_meas.measure(0, 0)

        result = simulator.run(qc_meas, shots=shots).result()
        counts = result.get_counts(qc_meas)
        count0 = counts.get('0', 0)
        p_i = count0 / shots
        p_i_list.append(p_i)
    return p_i_list

###############################################################################
# 重构密度矩阵
###############################################################################
def reconstruct_rho_from_sic_povm_prob(p_list):
    """
    根据 SIC-POVM 测量概率 [p0, p1, p2, p3] 重构密度矩阵 \rho。
    线性重构: \rho = sum_i p_i * 2 * (1/2)|phi_i><phi_i|
    """
    rho_est = np.zeros((2, 2), dtype=complex)
    for i, phi in enumerate(povm_phis):
        ketbra = np.outer(phi, np.conjugate(phi))
        Pi_i = 0.5 * ketbra  # POVM 元素
        rho_est += p_list[i] * 2.0 * Pi_i

    # 确保 Hermitian 和正定性
    rho_est = 0.5 * (rho_est + rho_est.conjugate().T)
    vals, vecs = np.linalg.eigh(rho_est)
    vals_clipped = np.clip(vals, 0, None)
    s = np.sum(vals_clipped)
    if s < 1e-12:
        rho_est = np.eye(2) / 2  # 防止全零
    else:
        vals_clipped /= s
        rho_est = vecs @ np.diag(vals_clipped) @ vecs.conjugate().T

    return rho_est

###############################################################################
# 执行单比特 POVM 断层测量
###############################################################################
def perform_single_qubit_povm_tomography(qc_original, shots=1024):
    """
    对单量子比特电路执行 SIC-POVM 测量并重构密度矩阵。
    """
    p_list = measure_probabilities_sic_povm(qc_original, shots=shots)
    rho_est = reconstruct_rho_from_sic_povm_prob(p_list)
    return rho_est