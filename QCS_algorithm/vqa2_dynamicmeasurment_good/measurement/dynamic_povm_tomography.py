
import numpy as np
from qiskit import QuantumCircuit, ClassicalRegister, Aer
from qiskit.providers.aer import AerSimulator
from qiskit.extensions import UnitaryGate

# 定义SIC-POVM的四个向量
phi0 = np.array([1.0, 0.0], dtype=complex)
phi1 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2)], dtype=complex)
phi2 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 2 * np.pi / 3)], dtype=complex)
phi3 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 4 * np.pi / 3)], dtype=complex)
povm_phis = [phi0, phi1, phi2, phi3]

def _get_unitary_map_phi_to_0(phi):
    """返回酉矩阵 U，使得 U|0> = phi"""
    norm = np.linalg.norm(phi)
    phi_n = phi / norm
    a, b = phi_n[0], phi_n[1]
    orth = np.array([-np.conjugate(b), np.conjugate(a)], dtype=complex)
    orth /= np.linalg.norm(orth)
    U = np.column_stack([phi_n, orth])
    return U

def measure_probabilities_dynamic_povm(qc_original, shots=1024):
    """
    对单量子比特电路执行动态POVM测量，返回概率 [p0, p1, p2, p3]
    """
    simulator = AerSimulator()
    p_i_list = []

    for i, phi in enumerate(povm_phis):
        qc_meas = qc_original.copy()
        U_i = _get_unitary_map_phi_to_0(phi)
        U_i_dagger = np.conjugate(U_i.T)
        gate = UnitaryGate(U_i_dagger, label=f"Dynamic_POVM_{i}")
        qc_meas.append(gate, [0])

        creg = ClassicalRegister(1)
        qc_meas.add_register(creg)
        qc_meas.measure(0, 0)

        result = simulator.run(qc_meas, shots=shots).result()
        counts = result.get_counts(qc_meas)
        count0 = counts.get('0', 0)
        p_i = count0 / shots
        p_i_list.append(p_i)
    return p_i_list

def reconstruct_rho_from_dynamic_povm_prob(p_list):
    """
    根据测量概率 [p0, p1, p2, p3] 重构密度矩阵 \rho
    重构公式: \rho = sum_i p_i * 2 * (1/2)|phi_i><phi_i|
    """
    rho_est = np.zeros((2, 2), dtype=complex)
    for i, phi in enumerate(povm_phis):
        ketbra = np.outer(phi, np.conjugate(phi))
        Pi_i = 0.5 * ketbra  # POVM算符
        rho_est += p_list[i] * 2.0 * Pi_i

    # 确保Hermitian性和正定性
    rho_est = 0.5 * (rho_est + rho_est.conjugate().T)
    vals, vecs = np.linalg.eigh(rho_est)
    vals_clipped = np.clip(vals, 0, None)
    if np.sum(vals_clipped) < 1e-12:
        rho_est = np.eye(2) / 2  # 若无效，返回均匀混合态
    else:
        vals_clipped /= np.sum(vals_clipped)  # 归一化迹
        rho_est = vecs @ np.diag(vals_clipped) @ vecs.conjugate().T

    return rho_est

def perform_single_qubit_dynamic_povm_tomography(qc_original, shots=1024):
    """
    执行动态POVM断层测量并重构密度矩阵
    """
    p_list = measure_probabilities_dynamic_povm(qc_original, shots=shots)
    rho_est = reconstruct_rho_from_dynamic_povm_prob(p_list)
    return rho_est