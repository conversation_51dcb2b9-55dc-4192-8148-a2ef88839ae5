import copy
import numpy as np

from tools.tools_depr import get_probability_array, compute_variance
from qiskit.quantum_info import DensityMatrix
from qiskit import A<PERSON>, transpile
from qiskit.quantum_info.states import partial_trace
from qiskit.quantum_info import purity as get_purity
from qiskit import <PERSON>Register, ClassicalRegister, QuantumCircuit
from IO import get_logger

logger = get_logger(__name__)
SIMULATOR = Aer.get_backend("aer_simulator")


def measure_sigma_exact(qc):  # exact!
    """
    Determines the expectation value of Pauli-Z operator by exact density
    matrix reconstruction (cheat-method, no estimation). Assumes ancilla qubit
    is 0-th qubit in qc.

    Args:
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla

    """
    global SIMULATOR
    qc.save_statevector()
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ).result()

    state_final = DensityMatrix(result.get_statevector())
    traced_over = list(range(1, qc.num_qubits))
    rho_final = partial_trace(state_final, traced_over)
    purity = 2 * np.real(get_purity(rho_final)) - 1
    p_0 = rho_final.data[0][0]
    p_1 = rho_final.data[1][1]
    sigma = np.real(p_0 - p_1)
    return sigma, rho_final, purity


def determine_rho_exact(qc):  # exact!
    """
    Estimates the density matrix of single-ancilla qubit after all quantum
    instructions by computing all-qubit density matrix and tracing out all
    other qubits. Assumes ancilla qubit is 0-th qubit in qc.

    Args:
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla

    """
    global SIMULATOR
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ).result()

    state_final = DensityMatrix(result.get_statevector())
    traced_over = list(range(1, qc.num_qubits))
    rho_final = partial_trace(state_final, traced_over)
    # print(rho_final)
    # logger.info(f"Purity: {2 * np.real(rho_final.purity()) - 1}")
    purity = get_purity(rho_final)
    return rho_final, purity


def measure_sigma_plain(qc, shots):
    """
    Estimates the expectation value of Pauli-Z operator by using plain sampling
    in computational basis. Assumes ancilla qubit is 0-th qubit in qc.

    Args:
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla

    """
    global SIMULATOR
    # logger.info(f"Plain: Used number of shots: {shots}")
    creg = ClassicalRegister(1)
    qc.add_register(creg)
    qc.measure(0, 0)
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ, shots=shots).result()
    counts = result.get_counts(t_circ)
    prob_arr = get_probability_array(counts, shots)
    sigma = np.sum(np.array([1, -1]) * prob_arr)
    return sigma


def measure_sigma_by_povm(unitary, omega_arr, shots, qc):
    """
    Estimates the expectation value of Pauli-Z operator by POVM two-qubit measurement
    scheme. Assumes ancilla qubit is 0-th qubit in qc.
    Args:
        unitary: Unitary matrix, constructed in POVM scheme
        omega_arr: POVM pseudo-eigenvalue vector
        shots: Number of shots for experiment
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla
        var: Variance of POVM sigma estimation
        prob_arr: Contains probabilities for binary string outcomes

    """
    global SIMULATOR
    # logger.info(f"POVM: Used number of shots: {shots}")
    qc = copy.deepcopy(qc)
    cr = ClassicalRegister(2)
    qc.add_register(cr)
    qc.reset(1)
    qc.unitary(unitary, [0, 1])
    qc.measure([0, 1], [0, 1])
    t_qc = transpile(qc)
    result = SIMULATOR.run(t_qc, shots=shots).result()
    counts = result.get_counts()
    prob_arr = get_probability_array(counts, shots)
    sigma = np.real(np.sum(prob_arr * omega_arr))
    var = compute_variance(omega_arr, prob_arr, shots)
    del qc
    return sigma, var, prob_arr


def measure_sigma_exact_copy(qc):  # exact!
    """
    Estimates the expectation value of Pauli-Z operator by POVM two-qubit measurement
    scheme. Does a *COPY* of the network. Assumes ancilla qubit is 0-th qubit in qc.
    Args:
        unitary: Unitary matrix, constructed in POVM scheme
        omega_arr: POVM pseudo-eigenvalue vector
        shots: Number of shots for experiment
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla
        var: Variance of POVM sigma estimation
        prob_arr: Contains probabilities for binary string outcomes

    """
    global SIMULATOR
    qc = copy.deepcopy(qc)
    qc.save_statevector()
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ).result()

    state_final = DensityMatrix(result.get_statevector())
    traced_over = list(range(1, qc.num_qubits))
    rho_final = partial_trace(state_final, traced_over)
    # logger.info(f"Purity: {2 * np.real(rho_final.purity()) - 1}")
    p_0 = rho_final.data[0][0]
    p_1 = rho_final.data[1][1]
    sigma = np.real(p_0 - p_1)
    del qc
    return sigma

