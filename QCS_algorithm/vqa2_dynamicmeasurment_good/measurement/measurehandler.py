import measurement.ancilla as anc
import measurement.inputport as ip

from qiskit import Aer
from IO import get_logger
from measurement.povm_optimization import create_optimized_povm_measure

simulator = Aer.get_backend("aer_simulator")


logger = get_logger(__name__)


class MeasureHandler:
    """
    A class that takes care of the measurement process, each network of 'Network' is equipped by
    a 'MeasureHandler' - this gets called when the cost / sigma_z is getting estimated

    :: Future implementation may include tomography/measurement of many qubits
    """

    def __init__(self):
        self.measures = (
            {}
        )  # Contains 'Measure" function that can be called by its measure index
        self.povm_active = (
            False  # If stays 'False', no POVM will be used throughout optimization
        )
        self.memory_active = False
        self.rho_list = (
            []
        )  # Contains density matrix of some network (K/V/I, with possibly a shifted parameter
        self.pure_lst = []

    def estimate_sigma_ancilla(self, nw, shots):
        """
        Estimates the expectation value of Pauli-<PERSON> operator by either exact density
        matrix reconstruction (cheat-method, no estimation), using plain sampling, or
        POVM two-qubit sampling.
        Variable nw.meas_index only important, if adaptive scheme is used, the index fixes which
        adapted measurement gets called (these are saved in self.measures after optimization of
        measurement)

        Args:
            nw: Network object
            shots: Number of shots for experiment

        Returns:
            sigma: Expectation value of Pauli-Z Operator
        """
        if self.povm_active and nw.meas_index not in self.measures.keys():
            self.measures[nw.meas_index] = create_optimized_povm_measure(
                nw.qc_assigned, shots=shots
            )

        if nw.meas_index in self.measures.keys():
            sigma = self.measures[nw.meas_index](nw, shots)
        else:
            if shots == 0:
                sigma, rho, purity = anc.measure_sigma_exact(nw.qc_assigned)
                if nw.meas_index == 0:
                    # logger.info(f"INDEX 0 {nw.type}")
                    # rho, purity = anc.determine_rho_exact(nw.qc_assigned)
                    if nw.type == "K":
                        # logger.info(f"Purity K: {purity}")
                        self.rho_list.append(rho)
                        # logger.info(f"LEN: {len(self.rho_list)}")
                        self.pure_lst += [purity]
                    elif nw.type == "V":
                        # logger.debug(f"Purity V: {purity}")
                        self.rho_list.append(rho)
                        self.pure_lst += [purity]
            else:
                sigma = anc.measure_sigma_plain(nw.qc_assigned, shots)
        return sigma

    @staticmethod
    def measure_all(qc, shots):
        counts = ip.measure_all(qc, shots)
        return counts
