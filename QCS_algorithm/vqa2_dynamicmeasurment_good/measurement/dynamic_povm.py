
import numpy as np
from qiskit import QuantumCircuit, Aer
from qiskit.providers.aer import AerSimulator

# 定义 SIC-POVM 向量
phi0 = np.array([1.0, 0.0], dtype=complex)
phi1 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2)], dtype=complex)
phi2 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 2 * np.pi / 3)], dtype=complex)
phi3 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 4 * np.pi / 3)], dtype=complex)
povm_phis = [phi0, phi1, phi2, phi3]

def get_params_for_phi(phi):
    """
    计算变分电路参数，使其实现 |0> -> |phi>
    变分电路形式：RY(theta0) RZ(theta1)
    """
    theta0 = 2 * np.arccos(np.abs(phi[0]))
    theta1 = np.angle(phi[1]) - np.angle(phi[0]) if np.abs(phi[0]) > 1e-10 else 0.0
    return [theta0, theta1]

def measure_probability(qc_assigned, shots=1024):
    """
    执行 Z 基测量，返回 P(|0>)
    """
    simulator = AerSimulator()
    qc_meas = qc_assigned.copy()
    qc_meas.measure_all()
    result = simulator.run(qc_meas, shots=shots).result()
    counts = result.get_counts(qc_meas)
    p0 = counts.get('0', 0) / shots
    return p0

def dynamic_povm_measurement(ansatz, shots=1024):
    """
    执行动态 POVM 测量，返回测量概率列表 p_list
    """
    p_list = []
    for phi in povm_phis:
        params = get_params_for_phi(phi)
        qc_assigned = ansatz.assign_parameters(params)
        p_i = measure_probability(qc_assigned, shots=shots)
        p_list.append(p_i)
    return p_list

def reconstruct_rho(p_list):
    """
    根据测量概率 p_list 重构密度矩阵
    """
    rho_est = np.zeros((2, 2), dtype=complex)
    for i, phi in enumerate(povm_phis):
        ketbra = np.outer(phi, np.conjugate(phi))
        E_i = 0.5 * ketbra  # E_i = 1/2 |phi_i><phi_i|
        rho_est += p_list[i] * 2.0 * E_i
    # 确保 Hermitian
    rho_est = 0.5 * (rho_est + rho_est.conjugate().T)
    # 确保正定和迹为 1
    vals, vecs = np.linalg.eigh(rho_est)
    vals_clipped = np.clip(vals, 0, None)
    s = np.sum(vals_clipped)
    if s < 1e-12:
        rho_est = np.eye(2) / 2  # 默认均匀混合态
    else:
        vals_clipped /= s
        rho_est = vecs @ np.diag(vals_clipped) @ vecs.conjugate().T
    return rho_est

