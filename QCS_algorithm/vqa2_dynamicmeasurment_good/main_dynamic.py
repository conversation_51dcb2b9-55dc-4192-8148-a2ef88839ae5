
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
from qiskit import QuantumCir<PERSON>it, Aer, transpile
from qiskit.circuit import ParameterVector
from measurement.dynamic_povm import dynamic_povm_measurement, reconstruct_rho

# 定义变分电路类
class SingleQubitAnsatz:
    def __init__(self):
        self.qc = QuantumCircuit(1)
        self.num_parameters = 2
        self.param_vector = ParameterVector("theta", self.num_parameters)
        self.qc.ry(self.param_vector[0], 0)
        self.qc.rz(self.param_vector[1], 0)

    def assign_parameters(self, params):
        assign_dict = {self.param_vector[i]: params[i] for i in range(self.num_parameters)}
        return self.qc.assign_parameters(assign_dict)

# 计算矩阵的平方根
def matrix_sqrt(mat):
    vals, vecs = np.linalg.eigh(mat)  # 使用 eigh，因为密度矩阵是 Hermitian 的
    vals_sqrt = np.sqrt(np.clip(vals, 0, None))  # 确保特征值非负
    return vecs @ np.diag(vals_sqrt) @ vecs.conjugate().T

# 计算保真度
def fidelity(rho, sigma):
    sqrt_rho = matrix_sqrt(rho)
    temp = sqrt_rho @ sigma @ sqrt_rho
    temp_sqrt = matrix_sqrt(temp)
    return np.real(np.trace(temp_sqrt)) ** 2

# 主函数
def main():
    # 1. 准备目标量子态（例如 |+> 态）
    target_qc = QuantumCircuit(1)
    target_qc.h(0)  # 目标态 |+> = (|0> + |1>)/sqrt(2)
    sim = Aer.get_backend("statevector_simulator")
    target_state = sim.run(transpile(target_qc, sim)).result().get_statevector()
    target_state = np.asarray(target_state)  # 转换为 NumPy 数组
    target_rho = np.outer(target_state, np.conjugate(target_state))

    # 2. 初始化变分电路
    ansatz = SingleQubitAnsatz()

    # 3. 执行动态 POVM 测量
    shots = 8192
    p_list = dynamic_povm_measurement(ansatz, shots=shots)
    print("Measurement probabilities:", p_list)

    # 4. 重构密度矩阵
    rho_est = reconstruct_rho(p_list)

    # 5. 计算保真度并输出结果
    fid = fidelity(rho_est, target_rho)
    print("\nReconstructed density matrix:\n", rho_est)
    print("\nTarget density matrix:\n", target_rho)
    print(f"\nFidelity: {fid:.6f}")

if __name__ == "__main__":
    main()