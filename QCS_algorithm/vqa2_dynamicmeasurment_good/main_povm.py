
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import scipy
from scipy.optimize import minimize
from qiskit import Quantum<PERSON><PERSON>cuit, Aer, transpile
from qiskit.circuit import ParameterVector
from measurement.povm_tomography import perform_single_qubit_povm_tomography

###############################################################################
# 1) 单比特电路(2参数): RY -> RZ
###############################################################################
class SingleQubitAnsatz:
    """
    2-parameter circuit:
      circuit = RY(θ0) -> RZ(θ1).
    Enough to represent any single-qubit pure state.
    """
    def __init__(self):
        self.qc = QuantumCircuit(1)
        self.num_parameters = 2
        self.param_vector = ParameterVector("theta", self.num_parameters)

        self.qc.ry(self.param_vector[0], 0)
        self.qc.rz(self.param_vector[1], 0)

    def assign_parameters(self, params):
        assign_dict = {self.param_vector[i]: params[i] for i in range(self.num_parameters)}
        return self.qc.assign_parameters(assign_dict)

###############################################################################
# 2) 保真度定义: w.r.t. |1> => real(rho[1,1])
###############################################################################
def fidelity_with_1(rho):
    return np.real(rho[1,1])

###############################################################################
# 3) 成本函数: cost = 1 - fidelity
###############################################################################
class VQACost:
    def __init__(self, ansatz):
        self.ansatz = ansatz
        self.delta = 1e-5

    def cost(self, params):
        qc_assigned = self.ansatz.assign_parameters(params)
        rho_est = perform_single_qubit_povm_tomography(qc_assigned, shots=8192)
        fid = fidelity_with_1(rho_est)
        return 1.0 - fid

    def cost_and_grad(self, params):
        cval = self.cost(params)
        grad = np.zeros_like(params)
        for i in range(len(params)):
            plus = np.copy(params)
            plus[i] += self.delta
            c_plus = self.cost(plus)
            minus = np.copy(params)
            minus[i] -= self.delta
            c_minus = self.cost(minus)
            grad[i] = (c_plus - c_minus) / (2 * self.delta)
        return cval, grad

###############################################################################
# 4) 主函数: 多次随机重启, L-BFGS-B, 目标保真度 > 0.95
###############################################################################
def main():
    ans = SingleQubitAnsatz()
    cost_manager = VQACost(ans)

    print("Ansatz circuit (symbolic) = ")
    print(ans.qc)

    N_RESTARTS = 10  # 增加重启次数以逃离局部最优
    best_fid = 0.0
    best_params = None

    for attempt in range(1, N_RESTARTS + 1):
        init_params = np.random.rand(ans.num_parameters) * 2 * np.pi
        print(f"\n=== Attempt #{attempt}, initial params={init_params} ===")
        print("RUNNING THE L-BFGS-B CODE")

        def fun_and_jac(x):
            val, grad = cost_manager.cost_and_grad(x)
            return val, grad

        res = scipy.optimize.minimize(
            fun_and_jac,
            init_params,
            jac=True,
            method='L-BFGS-B',
            options={'maxiter': 300, 'gtol': 1e-14, 'ftol': 1e-14, 'disp': True}
        )
        final_cost = res.fun
        final_fid = 1.0 - final_cost
        print(f"At end of attempt #{attempt}: cost={final_cost:.6f}, fidelity={final_fid:.6f}")

        if final_fid > best_fid:
            best_fid = final_fid
            best_params = res.x

        if final_fid >= 0.95:
            print(f"Reached fidelity >= 0.95, skip remaining attempts.")
            break

    print("\n============================")
    print("All attempts done!")
    print(f"Best fidelity found= {best_fid:.6f}")

    if best_params is not None:
        qc_final = ans.assign_parameters(best_params)
        print("Best params:", best_params)
        print("\nFinal assigned circuit =")
        print(qc_final)

        rho_final = perform_single_qubit_povm_tomography(qc_final, shots=8192)
        print("\nReconstructed final rho =")
        print(rho_final)
        print("Diagonal=", np.diag(rho_final))
        print(f"\nFinal Fidelity= {fidelity_with_1(rho_final):.6f}")

        # 验证真实状态
        sim = Aer.get_backend("statevector_simulator")
        t_circ = transpile(qc_final, sim)
        result = sim.run(t_circ).result()
        psi = result.get_statevector(t_circ)
        rho_true = np.outer(psi, psi.conj())
        print("\nTrue rho (statevector) =")
        print(rho_true)
        print("True fidelity= ", fidelity_with_1(rho_true))
    else:
        print("No solution found. This is unexpected for single-qubit ideal code!")

if __name__ == "__main__":
    main()