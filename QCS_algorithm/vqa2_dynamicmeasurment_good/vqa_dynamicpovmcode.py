

我给你第二部分代码：关于在变分量子电路中用povm基进行动态测量的1个量子比特的量子断层测量的方法的代码，请深度思考和认真理解这些代码。注意这些代码的逻辑模块的大致如下：


下边所有的这些代码的逻辑结构如下：


vqa_new
├── IO
│   ├── hdf5handler.py      # 与 HDF5 格式文件的读写、管理相关
│   ├── inputhandler.py     # 从输入文件/脚本读取并解析参数，或生成脚本
│   └── logger.py           # 封装了基于 python logging 库的日志系统
│
├── measurement
│   ├── ancilla.py          # 利用辅助量子比特(ancilla)进行测量的函数
│   ├── inputport.py        # “input port”测量方法封装
│   ├── measurehandler.py   # 统一的测量管理器，可根据需求切换不同测量模式(POVM / Plain Z测量 等)
│   ├── dynamic_povm_tomography.py  
│   ├── povm_tomography.py 
│   ├── dynamic_povm.py 
│   └── povm_optimization.py# 与POVM(尤其可优化POVM)相关的算法实现
│
├── mpslibrary
│   ├── mpdcircuit.py       # 实现 MPD (Matrix Product Decomposition) 量子电路
│   ├── mpdunitaries.py     # 生成 MPD 所需的多体 UnitaryGate 等操作
│   ├── operations.py       # MPS/张量网络的一些常用操作 (QR/SVD 分解, 张量收缩等)
│   └── ttlowdim.py         # 低维张量网络(TT格式)的多项式展开
│
├── optimization
│   ├── optimizers.py       # 各种优化算法(ADAM, BFGS, SPSA 等)的封装
│   └── schedulers.py       # 学习率调度策略(LR decay等)
│
├── QM
│   ├── operator.py         # 量子算符(如带周期边界的拉普拉斯算子等)及其操作
│   └── statevector.py      # 用 numpy 数组封装的“状态向量”类，支持多种态操作
│
└── tools
    ├── parameters.py       # 生成参数矩阵之类的工具函数
    ├── postprocess.py      # 后处理：将测量结果转化为期望值、方差等
    └── tools_depr.py       # 部分旧版或兼容性工具函数(已废弃但可能仍被使用)
│
├── ansatz.py               # 定义变分量子电路(如单比特/多比特 SU2 ansatz)的相关类
├── networks.py             # 定义不同类型的量子网络(Kinetic, Potential, NonLinear...)并组合计算能量等
├── main_dynamic_2.py       # 主函数示例：针对单量子比特，用动态POVM测量做量子态断层+变分优化




具体的这些代码如下：

子函数1 ：hdf5handler.py


import h5py
import os
import pathlib


from mpi4py import MPI
import numpy as np

comm = MPI.COMM_WORLD
rank = comm.Get_rank()
h5py.get_config().track_order = True


# TODO Rename to HDF5Handler
# TODO Path does not have to bne complicated, just sth.hdf5 - not all attributes needed
# TODO Class method which returns HDF5 Handler from hdf5 file
class HDF5Handler:
    def __init__(self, mode="w", dir="dump", index=1, parallel=False):
        self.parallel = parallel
        self.f, self.filename = self.open_or_create_file(mode, dir, index)
        self.main_grp = self.f.create_group("Main group")
        self.sim_grp = self.main_grp.create_group("Simulation")
        self.state_grp = self.main_grp.create_group("Final state")
        self.it_offset = 0
        self.f.attrs["#MPI cores"] = comm.size

    def set_attributes(self, attrs):
        for key, val in attrs.items():
            if isinstance(val, dict):
                val = str(val)
            self.f.attrs[key] = val

    def save_iteration(self, cost, true_cost, gradient, params, anc_densities, iteration):
        self.it_offset = iteration
        iter_grp = self.sim_grp.create_group(f"{iteration}")
        iter_grp.create_dataset("Cost", data=cost, dtype="float64")
        iter_grp.create_dataset("True Cost", data=true_cost, dtype="float64")
        iter_grp.create_dataset("Parameters", data=params, dtype="float64")
        iter_grp.create_dataset("Gradient", data=gradient, dtype="float64")
        for term, rho in anc_densities:
            iter_grp.create_dataset(f"{term}_rho_real", data=np.real(rho), dtype="float64")
            iter_grp.create_dataset(f"{term}_rho_imag", data=np.imag(rho), dtype="float64")
        # cost_dset[()] = cost
        # true_cost_dset[()] = true_cost
        # params_dset[:] = params

    def save_final_state(self, state):
        self.state_grp.create_dataset(
            "final_state_real", data=state.get_real_part(), dtype="f"
        )
        self.state_grp.create_dataset(
            "final_state_imag", data=state.get_imag_part(), dtype="f"
        )

    def open_or_create_file(self, mode, dir, index):
        os.makedirs(name=dir, exist_ok=True)
        filepath = f"{dir}/output_{index}.hdf5"
        print("HDF5Handler: Filepath: ", filepath, flush=True)
        if self.parallel:
            f = h5py.File(filepath, mode, driver="mpio", comm=comm)
        else:
            f = h5py.File(filepath, mode)
        return f, filepath

    @staticmethod
    def generate_path_from_attrs_depr(attrs):
        attrs_str_general = "_".join(
            [f"{tup[0]}_{tup[1]}" for tup in attrs["General"].items()]
        )
        attrs_str_opt = "_".join(
            [f"{tup[0]}_{tup[1]}" for tup in attrs["Optimization"].items()]
        )
        attrs_str = "_".join([attrs_str_general, attrs_str_opt])
        dump_folder = pathlib.PurePath(os.getcwd(), "dump")
        if not os.path.exists(dump_folder):
            os.makedirs(dump_folder)
        path = pathlib.PurePath(dump_folder, attrs_str + ".hdf5")
        return path


子函数2: inputhandler.py

import numpy as np
import os
import ast

def create_scripts_for_parameter_scan():
    parameters = {
        "n": 4,
        "k": 0,
        "reps": 3,
        "optimizer": "ADAM",
        "ADAM_IT": 5,
        "BETA_1": 0.999,
        "BETA_2": 0.98,
        "ETA": 0.001,
    }


SHELL_SCRIPT_STR = """#$ -q idefix.q
#$ -cwd
#$ -pe smp 1
#$ -l h_vmem=4.0G
#$ -l h_cpu=72:00:00
#$ -j y
#$ -m beas
#$ -M <EMAIL>
#$ -N vqa
#$ -o vqa.output

export PYTHONPATH='$PYTHONPATH:/afs/physnet.uni-hamburg.de/users/las_vqs/nvanhuel/Schreibtisch/Masterthesis/Code/vqa/'
. /afs/physnet.uni-hamburg.de/users/las_vqs/nvanhuel/Schreibtisch/Masterthesis/miniconda3/bin/activate
conda activate vqa

python3 /afs/physnet.uni-hamburg.de/users/las_vqs/nvanhuel/Schreibtisch/Masterthesis/Code/vqa/vqa/run.py
echo job finished
"""


def cast_attributes(attrs):
    for key_parent, val_parent in attrs.items():
        for key, val in val_parent.items():
            if isinstance(val, str):
                if "{" in val:
                    attrs[key_parent][key] = ast.literal_eval(val)
                    continue
            try:
                attrs[key_parent][key] = (
                    float(val) if not float(val).is_integer() else int(val)
                )
            except ValueError:
                pass

    if attrs["NSP"]["OMEGA_HO"] != 0:
        print("Updating Potential dict")
        attrs["NSP"]["POTENTIAL"]["HARMONIC"]["OMEGA"] = attrs["NSP"]["OMEGA_HO"]


class InputHandler:
    NSP = {
        "N": 4,
        "REPS": 1,
        "ANSATZ": "SU2",
        "TERMS": "K",
        "OMEGA_HO": 0,
        "POTENTIAL": "{'HARMONIC': {'OMEGA': 0}}",
        "G": 0,
        "LAYER_SCHEME": "YZ",
    }

    MSP = {"SHOTS": 1024, "POVM_IT": -1}

    OSP = {
        "SPSA_IT": 10,
        "OPTIMIZER": "ADAM",
        "ADAPT": "NO",
        "IT": 10,
        "BETA1": 0.9,
        "BETA2": 0.999,
        "EPSILON": 1e-8,
        "LR": 0.01,
        "START": 100,
        "ALPHA": 0.75,
        "OMEGA": 10,
        "GAMMA": 0.5,
        "NUM_FIT": 10,
        "NUM_OFF": 5,
        "NATGRAD": "NO"
    }

    def __init__(self):
        self.filename = f"{os.getcwd()}/input.txt"
        self.settings = None
        self.used_settings = {}

    # def write(self, attributes: dict):
    #     input_str = "GENERAL\n"
    #     for k, v in InputHandler.GENERAL.items():
    #         if k in attributes.keys():
    #             input_str += f"""{k}={attributes[k]}"""
    #         else:
    #             input_str += f"""{k}={v}"""
    #         input_str += """\n"""
    #
    #     input_str += "\nHYPERPARAMETERS\n"
    #     for key in attributes:
    #         if key not in InputHandler.GENERAL:
    #             input_str += f"""{key}={attributes[key]}"""
    #         input_str += """\n"""
    #     with open(self.filename, "w+") as f:
    #         f.write(input_str)

    def read(self):
        attrs_from_file = {"NSP": InputHandler.NSP, "MSP": InputHandler.MSP, "OSP": InputHandler.OSP}
        lines = open(self.filename).read().splitlines()
        index_msp = [i for i, v in enumerate(lines) if "MSP" in v][0]
        index_osp = [i for i, v in enumerate(lines) if "OSP" in v][0]
        for j, line in enumerate(lines):
            if "=" in line:
                if line[0] == "#":
                    continue
                key, val = line.split("=")
                if j < index_msp:
                    attrs_from_file["NSP"].update({key: val})
                elif j < index_osp:
                    attrs_from_file["MSP"].update({key: val})
                else:
                    attrs_from_file["OSP"].update({key: val})

        cast_attributes(attrs_from_file)
        self.settings = attrs_from_file

    def get(self, key, cat="NSP"):
        if key in self.settings[cat].keys():
            val = self.settings[cat][key]
            self.used_settings.update({key: val})
            return val
        else:
            if cat == "NSP":
                val = InputHandler.NSP[key]
                self.settings[cat].update({key: val})
                self.used_settings.update({key: val})
                return val
            elif cat == "MSP":
                val = InputHandler.MSP[key]
                self.settings[cat].update({key: val})
                self.used_settings.update({key: val})
                return val
            elif cat == "OSP":
                val = InputHandler.OSP[key]
                self.settings[cat].update({key: val})
                self.used_settings.update({key: val})
                return val
            else:
                raise ValueError(f"No default value for the key {key} known!")


class ShellScriptHandler:
    def __init__(self):
        self.filename = "../../runcode.sh"

    def write(self):
        with open(self.filename, "w+") as f:
            f.write(SHELL_SCRIPT_STR)


# ih = InputHandler()
# attrs = ih.read()
# a = list(attrs["General"]["TERMS"])
# print(a)


子函数3: logger.py

import logging
import logging.handlers
import os
import sys
from mpi4py import MPI


comm = MPI.COMM_WORLD
rank = comm.Get_rank()


logging.basicConfig(
    level=logging.WARNING, format=f"[%(name)s] :: [%(levelname)s] :: [%(message)s]"
)


def get_logger(name="root"):
    logger = logging.getLogger(name)
    try:
        level = os.environ["LEVEL"]
    except:
        print("Falling back to default info")
        level = "INFO"
    logger.setLevel(level)  # logging.INFO)
    fmt = f"[%(levelname)s][%(name)s][rank={rank}][%(message)s]"
    formatter = logging.Formatter(fmt)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    # file_handler = logging.FileHandler('debug.log', mode='w')
    # file_handler.setFormatter(formatter)

    logger.addHandler(console_handler)
    # logger.addHandler(file_handler)
    logger.propagate = False
    return logger


子函数4:  ancilla.py

import copy
import numpy as np

from tools.tools_depr import get_probability_array, compute_variance
from qiskit.quantum_info import DensityMatrix
from qiskit import Aer, transpile
from qiskit.quantum_info.states import partial_trace
from qiskit.quantum_info import purity as get_purity
from qiskit import QuantumRegister, ClassicalRegister, QuantumCircuit
from IO import get_logger

logger = get_logger(__name__)
SIMULATOR = Aer.get_backend("aer_simulator")


def measure_sigma_exact(qc):  # exact!
    """
    Determines the expectation value of Pauli-Z operator by exact density
    matrix reconstruction (cheat-method, no estimation). Assumes ancilla qubit
    is 0-th qubit in qc.

    Args:
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla

    """
    global SIMULATOR
    qc.save_statevector()
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ).result()

    state_final = DensityMatrix(result.get_statevector())
    traced_over = list(range(1, qc.num_qubits))
    rho_final = partial_trace(state_final, traced_over)
    purity = 2 * np.real(get_purity(rho_final)) - 1
    p_0 = rho_final.data[0][0]
    p_1 = rho_final.data[1][1]
    sigma = np.real(p_0 - p_1)
    return sigma, rho_final, purity


def determine_rho_exact(qc):  # exact!
    """
    Estimates the density matrix of single-ancilla qubit after all quantum
    instructions by computing all-qubit density matrix and tracing out all
    other qubits. Assumes ancilla qubit is 0-th qubit in qc.

    Args:
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla

    """
    global SIMULATOR
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ).result()

    state_final = DensityMatrix(result.get_statevector())
    traced_over = list(range(1, qc.num_qubits))
    rho_final = partial_trace(state_final, traced_over)
    # print(rho_final)
    # logger.info(f"Purity: {2 * np.real(rho_final.purity()) - 1}")
    purity = get_purity(rho_final)
    return rho_final, purity


def measure_sigma_plain(qc, shots):
    """
    Estimates the expectation value of Pauli-Z operator by using plain sampling
    in computational basis. Assumes ancilla qubit is 0-th qubit in qc.

    Args:
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla

    """
    global SIMULATOR
    # logger.info(f"Plain: Used number of shots: {shots}")
    creg = ClassicalRegister(1)
    qc.add_register(creg)
    qc.measure(0, 0)
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ, shots=shots).result()
    counts = result.get_counts(t_circ)
    prob_arr = get_probability_array(counts, shots)
    sigma = np.sum(np.array([1, -1]) * prob_arr)
    return sigma


def measure_sigma_by_povm(unitary, omega_arr, shots, qc):
    """
    Estimates the expectation value of Pauli-Z operator by POVM two-qubit measurement
    scheme. Assumes ancilla qubit is 0-th qubit in qc.
    Args:
        unitary: Unitary matrix, constructed in POVM scheme
        omega_arr: POVM pseudo-eigenvalue vector
        shots: Number of shots for experiment
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla
        var: Variance of POVM sigma estimation
        prob_arr: Contains probabilities for binary string outcomes

    """
    global SIMULATOR
    # logger.info(f"POVM: Used number of shots: {shots}")
    qc = copy.deepcopy(qc)
    cr = ClassicalRegister(2)
    qc.add_register(cr)
    qc.reset(1)
    qc.unitary(unitary, [0, 1])
    qc.measure([0, 1], [0, 1])
    t_qc = transpile(qc)
    result = SIMULATOR.run(t_qc, shots=shots).result()
    counts = result.get_counts()
    prob_arr = get_probability_array(counts, shots)
    sigma = np.real(np.sum(prob_arr * omega_arr))
    var = compute_variance(omega_arr, prob_arr, shots)
    del qc
    return sigma, var, prob_arr


def measure_sigma_exact_copy(qc):  # exact!
    """
    Estimates the expectation value of Pauli-Z operator by POVM two-qubit measurement
    scheme. Does a *COPY* of the network. Assumes ancilla qubit is 0-th qubit in qc.
    Args:
        unitary: Unitary matrix, constructed in POVM scheme
        omega_arr: POVM pseudo-eigenvalue vector
        shots: Number of shots for experiment
        qc: Quantum Circuit object

    Returns:
        sigma: Expectation value of Pauli-Z Operator of single ancilla
        var: Variance of POVM sigma estimation
        prob_arr: Contains probabilities for binary string outcomes

    """
    global SIMULATOR
    qc = copy.deepcopy(qc)
    qc.save_statevector()
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ).result()

    state_final = DensityMatrix(result.get_statevector())
    traced_over = list(range(1, qc.num_qubits))
    rho_final = partial_trace(state_final, traced_over)
    # logger.info(f"Purity: {2 * np.real(rho_final.purity()) - 1}")
    p_0 = rho_final.data[0][0]
    p_1 = rho_final.data[1][1]
    sigma = np.real(p_0 - p_1)
    del qc
    return sigma


子函数5: dynamic_povm_tomography.py


import numpy as np
from qiskit import QuantumCircuit, ClassicalRegister, Aer
from qiskit.providers.aer import AerSimulator
from qiskit.extensions import UnitaryGate

# 定义SIC-POVM的四个向量
phi0 = np.array([1.0, 0.0], dtype=complex)
phi1 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2)], dtype=complex)
phi2 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 2 * np.pi / 3)], dtype=complex)
phi3 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 4 * np.pi / 3)], dtype=complex)
povm_phis = [phi0, phi1, phi2, phi3]

def _get_unitary_map_phi_to_0(phi):
    """返回酉矩阵 U，使得 U|0> = phi"""
    norm = np.linalg.norm(phi)
    phi_n = phi / norm
    a, b = phi_n[0], phi_n[1]
    orth = np.array([-np.conjugate(b), np.conjugate(a)], dtype=complex)
    orth /= np.linalg.norm(orth)
    U = np.column_stack([phi_n, orth])
    return U

def measure_probabilities_dynamic_povm(qc_original, shots=1024):
    """
    对单量子比特电路执行动态POVM测量，返回概率 [p0, p1, p2, p3]
    """
    simulator = AerSimulator()
    p_i_list = []

    for i, phi in enumerate(povm_phis):
        qc_meas = qc_original.copy()
        U_i = _get_unitary_map_phi_to_0(phi)
        U_i_dagger = np.conjugate(U_i.T)
        gate = UnitaryGate(U_i_dagger, label=f"Dynamic_POVM_{i}")
        qc_meas.append(gate, [0])

        creg = ClassicalRegister(1)
        qc_meas.add_register(creg)
        qc_meas.measure(0, 0)

        result = simulator.run(qc_meas, shots=shots).result()
        counts = result.get_counts(qc_meas)
        count0 = counts.get('0', 0)
        p_i = count0 / shots
        p_i_list.append(p_i)
    return p_i_list

def reconstruct_rho_from_dynamic_povm_prob(p_list):
    """
    根据测量概率 [p0, p1, p2, p3] 重构密度矩阵 \rho
    重构公式: \rho = sum_i p_i * 2 * (1/2)|phi_i><phi_i|
    """
    rho_est = np.zeros((2, 2), dtype=complex)
    for i, phi in enumerate(povm_phis):
        ketbra = np.outer(phi, np.conjugate(phi))
        Pi_i = 0.5 * ketbra  # POVM算符
        rho_est += p_list[i] * 2.0 * Pi_i

    # 确保Hermitian性和正定性
    rho_est = 0.5 * (rho_est + rho_est.conjugate().T)
    vals, vecs = np.linalg.eigh(rho_est)
    vals_clipped = np.clip(vals, 0, None)
    if np.sum(vals_clipped) < 1e-12:
        rho_est = np.eye(2) / 2  # 若无效，返回均匀混合态
    else:
        vals_clipped /= np.sum(vals_clipped)  # 归一化迹
        rho_est = vecs @ np.diag(vals_clipped) @ vecs.conjugate().T

    return rho_est

def perform_single_qubit_dynamic_povm_tomography(qc_original, shots=1024):
    """
    执行动态POVM断层测量并重构密度矩阵
    """
    p_list = measure_probabilities_dynamic_povm(qc_original, shots=shots)
    rho_est = reconstruct_rho_from_dynamic_povm_prob(p_list)
    return rho_est

子函数6: dynamic_povm.py


import numpy as np
from qiskit import QuantumCircuit, Aer
from qiskit.providers.aer import AerSimulator

# 定义 SIC-POVM 向量
phi0 = np.array([1.0, 0.0], dtype=complex)
phi1 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2)], dtype=complex)
phi2 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 2 * np.pi / 3)], dtype=complex)
phi3 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 4 * np.pi / 3)], dtype=complex)
povm_phis = [phi0, phi1, phi2, phi3]

def get_params_for_phi(phi):
    """
    计算变分电路参数，使其实现 |0> -> |phi>
    变分电路形式：RY(theta0) RZ(theta1)
    """
    theta0 = 2 * np.arccos(np.abs(phi[0]))
    theta1 = np.angle(phi[1]) - np.angle(phi[0]) if np.abs(phi[0]) > 1e-10 else 0.0
    return [theta0, theta1]

def measure_probability(qc_assigned, shots=1024):
    """
    执行 Z 基测量，返回 P(|0>)
    """
    simulator = AerSimulator()
    qc_meas = qc_assigned.copy()
    qc_meas.measure_all()
    result = simulator.run(qc_meas, shots=shots).result()
    counts = result.get_counts(qc_meas)
    p0 = counts.get('0', 0) / shots
    return p0

def dynamic_povm_measurement(ansatz, shots=1024):
    """
    执行动态 POVM 测量，返回测量概率列表 p_list
    """
    p_list = []
    for phi in povm_phis:
        params = get_params_for_phi(phi)
        qc_assigned = ansatz.assign_parameters(params)
        p_i = measure_probability(qc_assigned, shots=shots)
        p_list.append(p_i)
    return p_list

def reconstruct_rho(p_list):
    """
    根据测量概率 p_list 重构密度矩阵
    """
    rho_est = np.zeros((2, 2), dtype=complex)
    for i, phi in enumerate(povm_phis):
        ketbra = np.outer(phi, np.conjugate(phi))
        E_i = 0.5 * ketbra  # E_i = 1/2 |phi_i><phi_i|
        rho_est += p_list[i] * 2.0 * E_i
    # 确保 Hermitian
    rho_est = 0.5 * (rho_est + rho_est.conjugate().T)
    # 确保正定和迹为 1
    vals, vecs = np.linalg.eigh(rho_est)
    vals_clipped = np.clip(vals, 0, None)
    s = np.sum(vals_clipped)
    if s < 1e-12:
        rho_est = np.eye(2) / 2  # 默认均匀混合态
    else:
        vals_clipped /= s
        rho_est = vecs @ np.diag(vals_clipped) @ vecs.conjugate().T
    return rho_est



子函数7: inputport.py

import copy
import numpy as np

from tools.tools_depr import compute_generator_expectation_values
from qiskit.quantum_info import DensityMatrix
from qiskit import Aer, transpile
from qiskit.quantum_info.states import partial_trace
from qiskit import QuantumRegister, ClassicalRegister, QuantumCircuit
from IO import get_logger

logger = get_logger(__name__)
SIMULATOR = Aer.get_backend("aer_simulator")


def measure_all(qc, shots):
    global SIMULATOR
    qc.measure_all()
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ, shots=shots).result()
    counts = result.get_counts(t_circ)
    return counts


子函数8: measurehandler.py

import measurement.ancilla as anc
import measurement.inputport as ip

from qiskit import Aer
from IO import get_logger
from measurement.povm_optimization import create_optimized_povm_measure

simulator = Aer.get_backend("aer_simulator")


logger = get_logger(__name__)


class MeasureHandler:
    """
    A class that takes care of the measurement process, each network of 'Network' is equipped by
    a 'MeasureHandler' - this gets called when the cost / sigma_z is getting estimated

    :: Future implementation may include tomography/measurement of many qubits
    """

    def __init__(self):
        self.measures = (
            {}
        )  # Contains 'Measure" function that can be called by its measure index
        self.povm_active = (
            False  # If stays 'False', no POVM will be used throughout optimization
        )
        self.memory_active = False
        self.rho_list = (
            []
        )  # Contains density matrix of some network (K/V/I, with possibly a shifted parameter
        self.pure_lst = []

    def estimate_sigma_ancilla(self, nw, shots):
        """
        Estimates the expectation value of Pauli-Z operator by either exact density
        matrix reconstruction (cheat-method, no estimation), using plain sampling, or
        POVM two-qubit sampling.
        Variable nw.meas_index only important, if adaptive scheme is used, the index fixes which
        adapted measurement gets called (these are saved in self.measures after optimization of
        measurement)

        Args:
            nw: Network object
            shots: Number of shots for experiment

        Returns:
            sigma: Expectation value of Pauli-Z Operator
        """
        if self.povm_active and nw.meas_index not in self.measures.keys():
            self.measures[nw.meas_index] = create_optimized_povm_measure(
                nw.qc_assigned, shots=shots
            )

        if nw.meas_index in self.measures.keys():
            sigma = self.measures[nw.meas_index](nw, shots)
        else:
            if shots == 0:
                sigma, rho, purity = anc.measure_sigma_exact(nw.qc_assigned)
                if nw.meas_index == 0:
                    # logger.info(f"INDEX 0 {nw.type}")
                    # rho, purity = anc.determine_rho_exact(nw.qc_assigned)
                    if nw.type == "K":
                        # logger.info(f"Purity K: {purity}")
                        self.rho_list.append(rho)
                        # logger.info(f"LEN: {len(self.rho_list)}")
                        self.pure_lst += [purity]
                    elif nw.type == "V":
                        # logger.debug(f"Purity V: {purity}")
                        self.rho_list.append(rho)
                        self.pure_lst += [purity]
            else:
                sigma = anc.measure_sigma_plain(nw.qc_assigned, shots)
        return sigma

    @staticmethod
    def measure_all(qc, shots):
        counts = ip.measure_all(qc, shots)
        return counts


子函数9: povm_optimization.py

import numpy as np
import measurement.ancilla as anc

from scipy import linalg
from qiskit import Aer
from optimization.optimizers import ADAM
from IO import get_logger

simulator = Aer.get_backend("aer_simulator")
logger = get_logger(__name__)


def construct_unitary_by_params(params):
    x_0, x_1, x_2, x_3, x_4, x_5, x_6, x_7 = params
    u_0 = np.asarray(
        [
            np.cos(x_0 * 2 * np.pi) * np.sin(x_1 * np.pi) * np.sin(x_2 * np.pi),
            np.sin(x_0 * 2 * np.pi) * np.sin(x_1 * np.pi) * np.sin(x_2 * np.pi),
            np.cos(x_1 * np.pi) * np.sin(x_2 * np.pi),
            np.cos(x_2 * np.pi),
        ]
    ).reshape((-1, 1))
    # with np.printoptions(precision=3, suppress=True):
    #     print(f"u_0 = \n{u_0}")
    #     print(f"Norm of u_0: {linalg.norm(u_0)}\n")
    r = np.asarray(
        [
            np.cos(x_3 * 2 * np.pi)
            * np.sin(x_4 * np.pi)
            * np.sin(x_5 * np.pi)
            * np.sin(x_6 * np.pi)
            * np.sin(x_7 * np.pi),
            np.sin(x_3 * 2 * np.pi)
            * np.sin(x_4 * np.pi)
            * np.sin(x_5 * np.pi)
            * np.sin(x_6 * np.pi)
            * np.sin(x_7 * np.pi),
            np.cos(x_4 * np.pi)
            * np.sin(x_5 * np.pi)
            * np.sin(x_6 * np.pi)
            * np.sin(x_7 * np.pi),
            np.cos(x_5 * np.pi) * np.sin(x_6 * np.pi) * np.sin(x_7 * np.pi),
            np.cos(x_6 * np.pi) * np.sin(x_7 * np.pi),
            np.cos(x_7 * np.pi),
        ]
    ).reshape((-1, 1))

    z = r[::2] + 1j * r[1::2]
    null_mat = linalg.null_space(np.conjugate(u_0).T)
    u_1 = (
        z[0] * null_mat[:, 0] + z[1] * null_mat[:, 1] + z[2] * null_mat[:, 2]
    ).reshape((-1, 1))
    # with np.printoptions(precision=3, suppress=True):
    #     print(f"u_1 = \n{u_1}")
    #     print(f"Norm of u_1: {linalg.norm(u_1)}")
    #     print(
    #         "Scalar product of u_0 and u_1: ",
    #         np.dot(np.conjugate(u_0).reshape(1, -1), u_1),
    #     )

    unitary_rel = np.hstack((u_0, u_1))
    null_mat = linalg.null_space(np.conjugate(unitary_rel).T)

    u_2 = (null_mat[:, 0] / np.linalg.norm(null_mat[:, 0])).reshape((-1, 1))
    u_3 = (null_mat[:, 1] / np.linalg.norm(null_mat[:, 1])).reshape((-1, 1))
    unitary_tot = np.hstack((u_0, u_1, u_2, u_3))
    if np.allclose(np.dot(np.conjugate(unitary_tot).T, unitary_tot), np.eye(4)):
        pass
    else:
        raise ValueError("Input matrix is not unitary")

    return unitary_tot


def construct_povm_effects(unitary):
    effects_lst = []
    for i in range(4):
        PI_i = np.outer(np.conjugate(unitary[i, :2]), unitary[i, :2])
        effects_lst.append(PI_i)

    effects_arr = np.asarray(effects_lst, dtype=np.complex128)
    if np.allclose(np.sum(effects_arr, axis=0), np.eye(2)):
        pass
    else:
        print("ERROR: Effects do not fulfill summing up to identity!")

    effects_arr = effects_arr.reshape((4, 1, -1))
    a_mat = np.vstack([effects_arr[:, 0, i] for i in range(4)])
    b = [1, 0, 0, -1]
    omega_arr = np.linalg.solve(a_mat, b)

    # Check decomposition of sigma_z into effects and omega_arr
    effects_arr = effects_arr.reshape((4, 2, 2))
    _check = np.zeros((2, 2), dtype=np.complex128)
    for i in range(4):
        _check += omega_arr[i] * effects_arr[i, :, :]

    sigma_z = np.asarray([[1, 0], [0, -1]])
    if np.allclose(_check, sigma_z):
        pass
    else:
        raise ValueError("Sigma_z decomposition is corrupted")

    return effects_arr, omega_arr


def decompose_shifted_effects(measured_effects_arr, shifted_effects_arr):
    d_mat = np.zeros((4, 4), dtype=np.complex128)
    for i in range(4):
        measured_effects_arr = measured_effects_arr.reshape((4, 1, -1))
        # print(measured_effects_arr[i])
        # print(shifted_effects_arr[i])
        a_mat = np.vstack([measured_effects_arr[:, 0, i] for i in range(4)])
        b = shifted_effects_arr[i].reshape(4)
        d_arr = np.linalg.solve(a_mat, b)
        # print(d_arr)
        d_mat[i, :] = d_arr

        measured_effects_arr = measured_effects_arr.reshape((4, 2, 2))
        _check = np.zeros((2, 2), dtype=np.complex128)
        for j in range(4):
            _check += d_arr[j] * measured_effects_arr[j, :, :]

    return d_mat


def measure_wrapper(params):
    unitary = construct_unitary_by_params(params)
    effects, omega_arr = construct_povm_effects(unitary)

    def measure(nw, shots):
        nonlocal params
        nonlocal unitary
        nonlocal effects
        nonlocal omega_arr
        if shots != 0:
            sigma, var, prob_arr = anc.measure_sigma_by_povm(
                unitary, omega_arr, shots, qc=nw.qc_assigned
            )
            # sigma2 = anc.measure_sigma_by_density_matrix(nw.qc_assigned, nw.type, nw.IPs[0])
            # logger.info(f"Povm compared to exact, {sigma}, exact: {sigma2}")
        else:
            sigma = anc.measure_sigma_exact_copy(nw.qc_assigned)
        return sigma

    return measure


class CostManagerPOVM:
    def __init__(self, qc, shots):
        self.var_arr = []
        self.sigma_arr = []
        self.qc = qc
        self.shots = shots

    def cost_fun(self, params):
        # For POVM optimization
        for param in params:
            if param == 0:
                param += 1e-5
            if param == 1:
                param -= 1e-5

        unitary = construct_unitary_by_params(params)
        effects, omega_arr = construct_povm_effects(unitary)
        sigma, var, prob_arr = anc.measure_sigma_by_povm(
            unitary, omega_arr, shots=self.shots, qc=self.qc
        )
        return sigma, var

    def cost_and_gradient_fun(self, params):
        # For POVM optimization, var and gradient
        num_params = len(params)
        for param in params:
            if param == 0:
                param += 1e-5
            if param == 1:
                param -= 1e-5

        unitary = construct_unitary_by_params(params)
        effects, omega_arr = construct_povm_effects(unitary)
        sigma, var, prob_arr = anc.measure_sigma_by_povm(
            unitary, omega_arr, shots=self.shots, qc=self.qc
        )

        h = 1e-3
        gradient = np.zeros(num_params)
        params_shifted_mat = np.vstack([params] * num_params)
        params_shifted_mat += np.eye(num_params) * h
        for k in range(num_params):
            params_shifted = params_shifted_mat[k]
            unitary_shifted = construct_unitary_by_params(params_shifted)
            effects_shifted_arr, omega_arr_shifted = construct_povm_effects(
                unitary_shifted
            )
            d_mat = decompose_shifted_effects(effects, effects_shifted_arr)
            second_moment = np.real(np.sum((omega_arr**2) * prob_arr))
            second_moment_shifted = np.real(
                np.sum(
                    np.sum(np.multiply(d_mat.T, (omega_arr_shifted**2)), axis=1)
                    * prob_arr
                )
            )
            d_xk = (second_moment_shifted - second_moment) / h
            gradient[k] = d_xk
        self.var_arr.append(var)
        self.sigma_arr.append(sigma)
        return sigma, var, gradient


def create_optimized_povm_measure(qc, shots, return_costmanager=False):
    # params = np.zeros(8)+1e-3
    params = np.random.random(8)
    costmanager = CostManagerPOVM(qc, shots=shots)
    logger.info(
        "##################### STARTING OPTIMIZATION OF POVM  ############################"
    )
    if shots != 0:
        adam = ADAM(IT=500, LR=0.001, BETA1=0.9, BETA2=0.999)
        new_params = adam.minimize(costmanager, params, iter_save=0)
    else:
        new_params = params

    logger.info(
        "################### Finished POVM optimization #######################"
    )
    measure = measure_wrapper(new_params)
    if return_costmanager:
        return measure, costmanager
    else:
        return measure


子函数10: povm_tomography.py


import numpy as np
from qiskit import QuantumCircuit, ClassicalRegister, Aer, transpile
from qiskit.providers.aer import AerSimulator
from qiskit.extensions import UnitaryGate

###############################################################################
# SIC-POVM 四个向量定义
###############################################################################
phi0 = np.array([1.0, 0.0], dtype=complex)
phi1 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2)], dtype=complex)
phi2 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 2 * np.pi / 3)], dtype=complex)
phi3 = 1 / np.sqrt(3) * np.array([1.0, np.sqrt(2) * np.exp(1j * 4 * np.pi / 3)], dtype=complex)
povm_phis = [phi0, phi1, phi2, phi3]

###############################################################################
# 辅助函数：计算将 phi 映射到 |0> 的酉矩阵
###############################################################################
def _get_unitary_map_phi_to_0(phi):
    """返回 2x2 酉矩阵 U，使得 U|0> = phi"""
    norm = np.linalg.norm(phi)
    phi_n = phi / norm

    a, b = phi_n[0], phi_n[1]
    orth = np.array([-np.conjugate(b), np.conjugate(a)], dtype=complex)
    orth /= np.linalg.norm(orth)
    U = np.column_stack([phi_n, orth])
    return U

###############################################################################
# SIC-POVM 测量概率
###############################################################################
def measure_probabilities_sic_povm(qc_original, shots=1024):
    """
    对单比特电路使用 SIC-POVM 测量，返回概率 [p0, p1, p2, p3]。
    """
    simulator = AerSimulator()
    p_i_list = []

    for i, phi in enumerate(povm_phis):
        qc_meas = qc_original.copy()
        qc_meas = transpile(qc_meas, simulator)

        U_i = _get_unitary_map_phi_to_0(phi)
        U_i_dagger = np.conjugate(U_i.T)
        gate = UnitaryGate(U_i_dagger, label=f"SIC_{i}")
        qc_meas.append(gate, [0])

        creg = ClassicalRegister(1)
        qc_meas.add_register(creg)
        qc_meas.measure(0, 0)

        result = simulator.run(qc_meas, shots=shots).result()
        counts = result.get_counts(qc_meas)
        count0 = counts.get('0', 0)
        p_i = count0 / shots
        p_i_list.append(p_i)
    return p_i_list

###############################################################################
# 重构密度矩阵
###############################################################################
def reconstruct_rho_from_sic_povm_prob(p_list):
    """
    根据 SIC-POVM 测量概率 [p0, p1, p2, p3] 重构密度矩阵 \rho。
    线性重构: \rho = sum_i p_i * 2 * (1/2)|phi_i><phi_i|
    """
    rho_est = np.zeros((2, 2), dtype=complex)
    for i, phi in enumerate(povm_phis):
        ketbra = np.outer(phi, np.conjugate(phi))
        Pi_i = 0.5 * ketbra  # POVM 元素
        rho_est += p_list[i] * 2.0 * Pi_i

    # 确保 Hermitian 和正定性
    rho_est = 0.5 * (rho_est + rho_est.conjugate().T)
    vals, vecs = np.linalg.eigh(rho_est)
    vals_clipped = np.clip(vals, 0, None)
    s = np.sum(vals_clipped)
    if s < 1e-12:
        rho_est = np.eye(2) / 2  # 防止全零
    else:
        vals_clipped /= s
        rho_est = vecs @ np.diag(vals_clipped) @ vecs.conjugate().T

    return rho_est

###############################################################################
# 执行单比特 POVM 断层测量
###############################################################################
def perform_single_qubit_povm_tomography(qc_original, shots=1024):
    """
    对单量子比特电路执行 SIC-POVM 测量并重构密度矩阵。
    """
    p_list = measure_probabilities_sic_povm(qc_original, shots=shots)
    rho_est = reconstruct_rho_from_sic_povm_prob(p_list)
    return rho_est


子函数11: mpdcircuit.py

import numpy as np

from mpslibrary.operations import (
    contractMPS,
    qr_decompose,
    pad_mps,
    contract_start_tensors,
)
from mpslibrary.ttlowdim import build_mps_polynomial
from qiskit import QuantumCircuit, QuantumRegister
from mpslibrary.mpdunitaries import build_mpd_unitaries_from_mps


def apply_mpd_unitaries(n, s, unitaries):
    qreg = QuantumRegister(n)
    qc = QuantumCircuit(qreg)
    if s == 1:
        for j, unitary in enumerate(unitaries.__reversed__()):
            if j != (len(unitaries) - 1):
                qc.append(unitary, [j + 1, j])
            else:
                qc.append(unitary, [j])
    elif s == 2:
        for j, unitary in enumerate(unitaries.__reversed__()):
            if j != (len(unitaries) - 1):
                qc.append(unitary, [j + 2, j + 1, j])
            else:
                qc.append(unitary, [j + 1, j])

    elif s == 3:
        for j, unitary in enumerate(unitaries.__reversed__()):
            if j != (len(unitaries) - 1):
                qc.append(unitary, [j + 3, j + 2, j + 1, j])
            else:
                qc.append(unitary, [j + 2, j + 1, j])

    return qc


def construct_mpd_circuit(n, a_ks, x_0=-1, x_n=1):
    # Determine s needed to capture required bond dimension
    s = int(np.ceil(np.log2(len(a_ks))))
    chi = 2**s

    # Build the MPS of the polynomial function, pad it to chi,
    # and do a QR decomposition. Dependent on chi, contract first
    # tensors, so dimension will match for building unitaries
    MPS, alpha = build_mps_polynomial(n, a_ks=a_ks, x_0=x_0, x_n=x_n)
    pad_mps(MPS, chi)
    MPS = qr_decompose(MPS)
    results = contractMPS(MPS)
    MPS = contract_start_tensors(MPS, s=s)

    # Having the MPS by hand, building the MPD unitaries to
    # encode this state on the qubits
    unitaries = build_mpd_unitaries_from_mps(MPS, chi)
    qc = apply_mpd_unitaries(n=n, s=s, unitaries=unitaries)
    return qc, alpha, results


# construct_mpd_circuit(4, [0, 0, 1], x_0=-1, x_n=1)


子函数12: mpdunitaries.py

import numpy as np

from qiskit.extensions import UnitaryGate
from scipy.linalg import null_space


def build_mpd_unitaries_from_mps(MPS, chi, d=2):
    unitaries = []
    length_mps = len(MPS)
    for j in range(length_mps):
        A_n = MPS[j]
        if j == 0:
            mps_mat = A_n.reshape((-1, chi))
            unitary_mat = mps_mat
            unitary = UnitaryGate(unitary_mat, label=f"G[{j}]")
            unitaries.append(unitary)
        elif j == (length_mps - 1):
            mps_mat = A_n.reshape((-1, A_n.shape[2]))
            unitary = np.zeros((d, d, chi, chi))
            null_mat = null_space(np.transpose(mps_mat))
            null_tensor = null_mat.reshape((2, -1, null_mat.shape[1]))
            unitary[0, :, :, 0] = mps_mat.reshape(d, chi)
            unitary[1, :, :, 0] = null_tensor[:, :, 0]
            for i in range(0, int((d * chi - 2) / 2)):
                unitary[0, :, :, i + 1] = null_tensor[:, :, 1 + i * 2]
                unitary[1, :, :, i + 1] = null_tensor[:, :, 2 + i * 2]
            # print("EINSUM", np.einsum("ijkl, mjko -> imlo", unitary, unitary)[0, 0, 0, 1])

            # physical, physical, virtual, virtual -> physical, virtual, physical, virtual
            unitary = np.einsum("ijkl-> jkli", unitary)
            unitary_mat = unitary.reshape((d * chi, d * chi))
            unitary = UnitaryGate(unitary_mat, label=f"G[{j}]")
            unitaries.append(unitary)

        else:
            mps_mat = A_n.reshape((-1, A_n.shape[2]))
            unitary = np.zeros((d, d, chi, chi))
            null_mat = null_space(np.transpose(mps_mat))
            null_tensor = null_mat.reshape((2, -1, null_mat.shape[1]))
            unitary[0, :, :, :] = A_n
            unitary[1, :, :, :] = null_tensor

            unitary = np.einsum(
                "ijkl-> jkli", unitary
            )  # phys, phys, virt, virt -> phys, virt, phys, virt  "ilkj"
            unitary_mat = unitary.reshape((chi * d, chi * d))
            unitary = UnitaryGate(unitary_mat, label=f"G[{j}]")
            unitaries.append(unitary)

    return unitaries


子函数13: operations.py

import numpy as np
import itertools

from scipy import linalg


def contractMPS(MPS):
    n = len(MPS)
    results = []
    # for binary_string in list(itertools.product("10", repeat=n)).__reversed__():
    # for binary_string in itertools.product("01", repeat=n):
    #     for m in range(n - 1):
    #         phy_index = int(binary_string[m])
    #         phy_index_po = int(binary_string[m + 1])
    #         if m == 0:
    #             result = MPS[m][phy_index]
    #         A_mpo = MPS[m + 1]
    #         result = np.einsum("jk, kl-> jl", result, A_mpo[phy_index_po])
    #
    #     result = result[0, 0]
    #     results.append(result)

    results = []
    for binary_string in itertools.product("01", repeat=n):
        result = [1]
        for m, bit in enumerate(binary_string):
            result = result @ MPS[m][int(bit)]

        result = result[0]
        results.append(result)

    return np.asarray(results)


def qr_decompose(MPS):
    for m in range(len(MPS) - 1):
        A_m = MPS[m]
        reshaped = np.reshape(A_m, (-1, A_m.shape[2]))
        qr_m = linalg.qr(reshaped, mode="economic")
        q, r = qr_m[0], qr_m[1]
        A_m_star = q.reshape((2, -1, q.shape[1]))
        MPS[m] = A_m_star
        A_mpo = MPS[m + 1]
        A_mpo_tilde = np.einsum("ij, kjm-> kim", r, A_mpo)
        MPS[m + 1] = A_mpo_tilde
        # print("IN QR", MPS[m + 1].shape)
    return MPS


def svd_decompose_mps(MPS, dim: int = 2):
    print(f"Doing SVD with dim {dim}")
    for i in range(len(MPS) - 1):
        A_m = MPS[i]
        reshaped = np.reshape(A_m, (-1, A_m.shape[2]))
        svd_m = linalg.svd(reshaped, full_matrices=False)
        u, s, vh = svd_m[0], np.diag(svd_m[1]), svd_m[2]
        if i == 0:
            scale_factor = 1
        else:
            scale_factor = np.power(np.sum((svd_m[1][:2]) ** 2), -1 / 2)
        u_tilde, s_tilde, vh_tilde = u[:, :dim], s[:dim, :dim], vh[:dim, :]
        # TODO: maybe implement a check if right/left-normalised, assert..
        u_tilde_reshaped = u_tilde.reshape((2, -1, u_tilde.shape[1]))
        svh = np.einsum("ij, jk-> ik", s_tilde, vh_tilde)
        MPS[i] = u_tilde_reshaped
        next_mps_tilde = np.einsum("ij, kjm-> kim", svh, MPS[i + 1])
        MPS[i + 1] = next_mps_tilde
    return MPS


#
# def pad_qr_mps(MPS, chi):
#     for i in range(len(MPS)):
#         # print(MPS[i].shape)
#         left_index = min(2 ** i, chi)
#         if i == (len(MPS) - 1):
#             right_index = 1
#         else:
#             right_index = min(2 ** (i + 1), chi)
#         MPS[i] = np.pad(
#             MPS[i],
#             pad_width=(
#                 (0, 0),
#                 (0, left_index - MPS[i].shape[1]),
#                 (0, right_index - MPS[i].shape[2]),
#             ),
#         )


def pad_mps(MPS, chi):  # Pad to needed bond dimension
    MPS[0] = np.pad(
        MPS[0],
        pad_width=(
            (0, 0),
            (0, 0),
            (0, chi - MPS[0].shape[2]),
        ),
    )

    for i in range(1, len(MPS) - 1):
        MPS[i] = np.pad(
            MPS[i],
            pad_width=(
                (0, 0),
                (0, chi - MPS[i].shape[1]),
                (0, chi - MPS[i].shape[2]),
            ),
        )

    MPS[-1] = np.pad(
        MPS[-1],
        pad_width=(
            (0, 0),
            (0, chi - MPS[-1].shape[1]),
            (0, 0),
        ),
    )


def contract_start_tensors(MPS, s):
    MPS_new = []
    for i, mps in enumerate(MPS):
        if s == 1:
            MPS_new.append(mps)

        elif s == 2:
            if i == 0:
                MPS[1] = np.einsum("ijk, mkl-> mijl", MPS[0], MPS[1])
                MPS_new.append(MPS[1])

            elif i == 1:
                pass

            else:
                MPS_new.append(mps)

        elif s == 3:
            if i == 0:
                MPS[1] = np.einsum("ijk, mkl-> mijl", MPS[0], MPS[1])

            elif i == 1:
                MPS[2] = np.einsum("mijk, qkl-> qmijl", MPS[1], MPS[2])
                MPS_new.append(MPS[2])

            elif i == 2:
                pass

            else:
                MPS_new.append(mps)

    return MPS_new


子函数14: ttlowdim.py

import numpy as np
import scipy

from math import factorial


def binomial_coefficient(k, s):
    c_sk = factorial(k) / (factorial(s) * factorial(k - s))
    return c_sk


def phi(s, p, x_1_star, a_ks):
    val = 0
    for k in range(s, p + 1):
        if k != s:
            val += a_ks[k] * binomial_coefficient(k, s) * x_1_star ** (k - s)
        else:
            val += a_ks[k] * binomial_coefficient(k, s)
    return val


def G(x_m, n, m, p, x_0, stepsize, a_ks=None):
    # See paper: Constructive Representation of Functions in Low-RankTensor Formats I.V. Oseledets
    x_m_star = x_m * 2 ** (n - m) * stepsize  # 2 / (2 ** n)
    if m == 1:
        G = np.zeros((1, p + 1), dtype=float)
        for s in range(p + 1):
            x_1_star = x_m_star + x_0  # 2 ** (n - 1)
            phi_s = phi(s, p, x_1_star, a_ks)
            G[0, s] = phi_s

    elif n > m:
        G = np.zeros((p + 1, p + 1), dtype=float)
        for j in range(p + 1):
            for i in range(j, p + 1):
                if i != j:
                    G[i, j] = binomial_coefficient(i, i - j) * x_m_star ** (
                        i - j
                    )  # k is lower index, s upper index
                else:
                    G[i, i] = binomial_coefficient(i, i - j)
    elif n == m:
        G = np.zeros((p + 1, 1), dtype=float)
        for i in range(0, p + 1):
            if i != 0:
                G[i, 0] = x_m_star**i
            else:
                G[i, 0] = 1
    return G


def normalize_polynomial_by_coefficients(n, a_ks, x_0, x_n):
    if isinstance(a_ks, np.ndarray):
        if a_ks.size != 0:
            if a_ks.shape[0] == a_ks.size:
                pass
            else:
                raise ValueError("Coefficient array has wrong dims")
        else:
            raise ValueError("Empty coefficient array")

    elif isinstance(a_ks, list):
        if a_ks:
            a_ks = np.asarray(a_ks)
        else:
            raise ValueError("Empty coefficient array")
    else:
        raise ValueError("Coefficient array is neither np.ndarray, nor list")

    x_coordinates, stepsize = np.linspace(
        x_0, x_n, num=2**n, endpoint=False, retstep=True
    )
    amplitudes = []
    for x in x_coordinates:
        val = a_ks[0]
        for k, a_k in enumerate(a_ks[1:], start=1):
            val += a_k * x**k
        amplitudes.append(val)
    normalization_factor = scipy.linalg.norm(amplitudes)
    return a_ks / normalization_factor, normalization_factor, stepsize


def build_mps_polynomial(n=4, a_ks=[0, 0, 1], x_0=-1, x_n=1):
    p = len(a_ks) - 1
    MPS = []
    a_ks, alpha, stepsize = normalize_polynomial_by_coefficients(
        n, a_ks, x_0=x_0, x_n=x_n
    )
    # print(a_ks)
    for m in range(1, n + 1):
        A_m = []
        for x_m in [0, 1]:
            if m == 1:
                g_mat = G(x_m, n, m, p, x_0, stepsize, a_ks)
            else:
                g_mat = G(x_m, n, m, p, x_0, stepsize)
            A_m.append(g_mat)
        A_m = np.asarray(A_m)
        MPS.append(A_m)
    return MPS, alpha

子函数15: optimizers.py

import time
import copy
import numpy as np
import os

os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"
import tensorflow as tf

if "OMP_NUM_THREADS" in os.environ:
    omp_num_threads = int(os.getenv("OMP_NUM_THREADS"))
    tf.config.threading.set_inter_op_parallelism_threads(omp_num_threads)
    tf.config.threading.set_intra_op_parallelism_threads(omp_num_threads)

import tensorflow_probability as tfp

from mpi4py import MPI
from IO import get_logger
from qiskit.algorithms.optimizers import SPSA
from vqa.optimization.termination import TerminationChecker, TerminationCheckerBFGS
from scipy.optimize import minimize
from optimization.schedulers import SlopeBasedDecay

tf.autograph.set_verbosity(1)
logger = get_logger(__name__)

comm = MPI.COMM_WORLD
rank = comm.Get_rank()


class Optimizer:
    def __init__(self, name, initial_learning_rate: float = 0.0, adapt="NO", **kwargs):
        self.name = name

        # Adapt hyperparameters
        self.initial_lr = copy.copy(initial_learning_rate)
        self.scheduler = SlopeBasedDecay(
            self, initial_learning_rate=initial_learning_rate, adapt=adapt, **kwargs
        )
        self.natgrad = False
        if "NATGRAD" in kwargs.keys():
            if kwargs["NATGRAD"] == "YES":
                self.natgrad = True

        # Saving information
        self.cost_lst = []
        self.dtheta_arr = []

    def transform_gradient(self, params, gradient):
        if rank == 0:
            if self.natgrad:
                fsmt = self.nw_ansatz.construct_blockdiag_fubini_study_metric_tensor(params)
                try:
                    fsmt_inv = np.linalg.inv(fsmt)
                except:
                    logger.info("Fubiny-Study Metric Tensor is singular!!")
                    fsmt += np.diag(np.ones(fsmt.shape[0]) * 1e-4)
                    fsmt_inv = np.linalg.inv(fsmt)
                logger.info("TRANSFORMING GRADIENT")
                return fsmt_inv @ gradient
            else:
                return gradient
        else:
            return gradient

    def get_cost_data(
        self, costmanager, params, t, hdf5handler=None, iter_save=1, order=0
    ):
        gradient = None
        if order == 0:
            cost = costmanager.cost_fun(params)
        elif order == 1:
            cost, gradient = costmanager.cost_and_gradient_fun(params)
            gradient = self.transform_gradient(params, gradient)
        else:
            raise NotImplementedError("Only zero and first order implemented!")

        self.cost_lst.append(cost)
        if iter_save != 0 and (t % iter_save) == 0:
            if rank == 0:
                true_cost = costmanager.true_cost_arr[-1]
                anc_densities = [(nw.type, nw.measure_handler.rho_list[-1])  # Adjust to be only real part of purity
                                 for nw in costmanager.networks if (nw.type in ["K", "V"])]
                hdf5handler.save_iteration(cost, true_cost, gradient, params, anc_densities, t)
                logger.info(f"{self.name}-Iteration: {t}; Cost={cost}")
                logger.info(f"{self.name}-Iteration: {t}; True Cost={true_cost}")

        if order == 0:
            return cost

        elif order == 1:
            return cost, gradient

    def check_termination(self):
        if len(self._cost_lst) > 30:
            rel_diff = (
                abs(self._cost_lst[-29] - self._cost_lst[-1]) / self._cost_lst[-29]
            )
            rel_diff2 = (
                abs(self._cost_lst[-30] - self._cost_lst[-2]) / self._cost_lst[-30]
            )
            # if rank == 0:
            #     logger.info(f"Rel. Diff: {rel_diff}")
            #     logger.info(self._cost_lst[-29])
            #     logger.info(self._cost_lst[-1])
            # # if rel_diff < 0.05 and rel_diff2 < 0.05:
            #     # return True

            # pp = np.polyfit(range(30), self._cost_lst[-30:], 1)
            # slope = pp[0]
            # if abs(slope) < 1e-2:
            #     print(slope)
            #     print(abs(slope))
            #     return True

            rel_diff = (
                abs(self._cost_lst[-29] - self._cost_lst[-1]) / self._cost_lst[-29] / 28
            )
            if rank == 0:
                logger.info(f"Rel. Diff: {rel_diff}")
            if rel_diff < 1e-4:
                return True
        return False

    def minimize(
        self, costmanager, initial_params, hdf5handler=None, iter_save=5, offset=0
    ):
        if self.name in ["ADAM", "ADAGRAD", "SGD", "NADAM", "ADAMAX"]:
            params = self.minimize_first_order(
                costmanager,
                initial_params,
                hdf5handler=hdf5handler,
                iter_save=iter_save,
                offset=offset,
            )
            return params

        elif self.name == "NATGRAD":
            params = self.minimize_custom(costmanager, initial_params, hdf5handler, iter_save=5, offset=0)
            return params

        elif self.name == "BFGS":
            params = self.minimize_custom(costmanager, initial_params, hdf5handler)
            return params

        elif self.name == "SPSA":
            params, cost_lst = self.minimize_custom(costmanager, initial_params)
            return params, cost_lst

    def minimize_zero_order(self, costmanager, initial_params):
        logger.debug(
            f"######### Starting {self.name} Optimization #########\n###### Using {self.it} as max iterations #####"
        )
        ## costmanager needs instance method 'cost_fun'
        result = self.optimizer.minimize(costmanager.cost_fun, initial_params)
        params = result.x
        cost_lst = self.term_checker.cost_lst
        cost_lst.append(result.fun)
        logger.debug(f"Final cost after zero order optimization: {result.fun}")
        return params, cost_lst

    def minimize_first_order(
        self, costmanager, initial_params, hdf5handler=None, iter_save=5, offset=0
    ):
        # costmanager needs instance methods cost_fun and cost_and_gradient_func
        params = initial_params
        if rank == 0:
            tf_params = tf.Variable(params)
            self.optimizer.build([tf_params])

        for t in range(offset, self.it + offset):
            cost, gradient = self.get_cost_data(
                costmanager, params, t, hdf5handler, iter_save, order=1
            )
            if rank == 0:
                gradient = tf.convert_to_tensor(gradient)
                self.optimizer.apply_gradients([(gradient, tf_params)])
                params = tf_params.numpy()

        self.get_cost_data(
            costmanager, params, self.it + offset, hdf5handler, iter_save, order=1
        )
        return params


##################################################################################
##################################################################################
#################        FIRST ORDER OPTIMIZERS           ########################
##################################################################################
##################################################################################


class SPSAOptimizer(Optimizer):
    def __init__(self, IT: int = 5, **kwargs):
        self.it = IT
        self.term_checker = TerminationChecker(100, self.it)
        self.optimizer = SPSA(maxiter=self.it, termination_checker=self.term_checker)
        super().__init__(name="SPSA", **kwargs)

    def minimize_custom(self, costmanager, initial_params):
        result = self.optimizer.minimize(costmanager.cost_fun, initial_params)
        params = result.x
        cost_lst = self.term_checker.cost_lst
        cost_lst.append(result.fun)
        logger.debug(f"Final cost after zero order optimization: {result.fun}")
        return params, cost_lst


##################################################################################
##################################################################################
#################        FIRST ORDER OPTIMIZERS           ########################
##################################################################################
##################################################################################


class BFGS(Optimizer):
    def __init__(
        self, nw_ansatz, adapt: str = "NO", IT=10, gtol: float = 10e-15, **kwargs
    ):
        self.it = IT
        super().__init__("BFGS", adapt=adapt, **kwargs)
        self.tf_minimize = tfp.optimizer.bfgs_minimize

    def minimize_custom(self, costmanager, initial_params, hdf5handler):
        logger.info("MINIMIZING")
        tf_params = tf.Variable(initial_params)
        result = self.tf_minimize(
            costmanager.cost_and_gradient_fun,
            tf_params,
            max_iterations=self.it,
            max_line_search_iterations=10,
            f_relative_tolerance=-10,
            f_absolute_tolerance=-10,
            stopping_condition=BFGS.stopping_condition,
        )
        print(result)
        print(result.num_objective_evaluations.numpy())
        if rank == 0:
            hdf5handler.set_attributes(
                {"Obj. evaluations": result.num_objective_evaluations.numpy()}
            )
        return result.position.numpy()

    @staticmethod
    def stopping_condition(a, b):
        return False

    # def minimize(self, costfunction, initial_params, hdf5handler):
    #     params = initial_params
    #     res = minimize(
    #         costfunction,
    #         params,
    #         method="L-BFGS-B",
    #         jac=True,
    #         callback=self.term_checker,
    #         options={"disp": False, "maxiter": self.it, "gtol": 0, "eps": -10},
    #     )
    #     params = res.x
    #
    #     return params


class ADAM(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        BETA1=0.9,
        BETA2=0.999,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.nw_ansatz = nw_ansatz
        self.m_t, self.v_t = 0, 0
        self.beta1 = BETA1
        self.beta2 = BETA2
        self.epsilon = EPSILON
        self.it = IT
        super().__init__(name="ADAM", initial_learning_rate=LR, adapt=adapt, **kwargs)
        self.optimizer = tf.keras.optimizers.Adam(
            learning_rate=self.scheduler, epsilon=self.epsilon
        )


class NADAM(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        BETA1=0.9,
        BETA2=0.999,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.nw_ansatz = nw_ansatz
        self.it = IT
        self.beta1 = BETA1
        self.beta2 = BETA2
        self.epsilon = EPSILON
        super().__init__(name="NADAM", initial_learning_rate=LR, adapt=adapt, **kwargs)
        self.optimizer = tf.keras.optimizers.Nadam(
            learning_rate=self.scheduler, epsilon=self.epsilon
        )


class ADAGRAD(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        BETA1=0.9,
        BETA2=0.999,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.nw_ansatz = nw_ansatz
        self.m_t, self.v_t = 0, 0
        self.beta1 = BETA1
        self.beta2 = BETA2
        self.epsilon = EPSILON
        self.it = IT
        super().__init__(
            name="ADAGRAD", initial_learning_rate=LR, adapt=adapt, **kwargs
        )
        self.optimizer = tf.keras.optimizers.Adagrad(
            learning_rate=self.scheduler, epsilon=1e-7
        )


class SGD(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        BETA1=0.9,
        BETA2=0.999,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.nw_ansatz = nw_ansatz
        self.m_t, self.v_t = 0, 0
        self.beta1 = BETA1
        self.beta2 = BETA2
        self.epsilon = EPSILON
        print(self.epsilon)
        self.it = IT
        super().__init__(name="SGD", initial_learning_rate=LR, adapt=adapt, **kwargs)
        self.optimizer = tf.keras.optimizers.experimental.SGD(
            learning_rate=self.scheduler
        )


class ADAMAX(Optimizer):
    def __init__(
        self,
        nw_ansatz=None,
        adapt: str = "NO",
        LR=0.001,
        EPSILON=1e-8,
        IT=10,
        **kwargs,
    ):
        self.ansatz = nw_ansatz
        self.it = IT
        self.epsilon = EPSILON
        self.lr = LR
        super().__init__(
            name="ADAMAX", initial_learning_rate=self.lr, adapt=adapt, **kwargs
        )
        self.optimizer = tf.keras.optimizers.Adamax(learning_rate=self.scheduler)


class NatGrad(Optimizer):
    def __init__(self, nw_ansatz, adapt, **kwargs):
        options = {
            "LR": 0.005,
            "IT": 100,
        }
        options.update(kwargs)  # Right now ignoring this
        self.nw_ansatz = nw_ansatz
        self.lr = options["LR"]
        self.it = options["IT"]
        super().__init__("NATGRAD", initial_learning_rate=self.lr, adapt=adapt, **kwargs)

    def do_step(self, params, gradient):
        FSMT = self.nw_ansatz.construct_blockdiag_fubini_study_metric_tensor(params)
        # print(self.nw_ansatz.qc)
        # print(FSMT.shape, gradient.shape)
        # print(FSMT)
        b = -1 * self.lr * gradient
        print(self.lr)
        try:
            d_theta = np.linalg.solve(FSMT, b)
        except:
            logger.info("Fubiny-Study Metric Tensor is singular!!")
            FSMT += np.diag(np.ones(FSMT.shape[0]) * 1e-4)
            d_theta = np.linalg.solve(FSMT, b)
        updated_params = params + d_theta
        self.dtheta_arr.append(np.linalg.norm(d_theta))
        return updated_params

    def minimize_custom(
        self, costmanager, initial_params, hdf5handler=None, iter_save=5, offset=0
    ):
        params = initial_params
        for t in range(offset, self.it + offset):
            cost, gradient = self.get_cost_data(
                costmanager, params, t, hdf5handler, iter_save, order=1
            )
            if rank == 0:
                params = self.do_step(params, gradient)

            # Update learning rate, if adaptive criterion is triggered
            self.lr = self.scheduler()

        self.get_cost_data(
            costmanager, params, self.it + offset, hdf5handler, iter_save, order=1
        )
        return params


def get_optimizer(name):
    if name.upper() == "ADAM":
        return ADAM
    elif name.upper() == "NADAM":
        return NADAM
    elif name.upper() == "ADAGRAD":
        return ADAGRAD
    elif name.upper() == "SGD":
        return SGD
    elif name.upper() == "ADAMAX":
        return ADAMAX
    elif name.upper() == "BFGS":
        logger.info("BFGS supported with SHOTS but should be used carefully!")
        return BFGS
    elif name.upper() == "SPSA":
        return SPSAOptimizer
    elif name.upper() == "NATGRAD":
        return NatGrad
    else:
        raise Exception(f"The optimizer {name} is not supported!")


子函数16: schedulers.py


import tensorflow as tf
import numpy as np

from mpi4py import MPI
from IO import get_logger

logger = get_logger(__name__)

comm = MPI.COMM_WORLD
rank = comm.Get_rank()


class SlopeBasedDecay(tf.keras.optimizers.schedules.LearningRateSchedule):
    def __init__(self, optimizer_inst, initial_learning_rate, adapt, **kwargs):
        self.optimizer_inst = optimizer_inst
        if adapt == "YES":
            self.on = True
        else:
            self.on = False

        self.learning_rate = initial_learning_rate
        adapt_params = {
            "NUM_FIT": 10,
            "NUM_OFF": 5,
            "ALPHA": 0.75,
            "OMEGA": 5,
            "GAMMA": 0.5,
            "START": 60
        }
        adapt_kwargs = {
            k: v for k, v in kwargs.items() if k in list(adapt_params.keys())
        }
        adapt_params.update(adapt_kwargs)
        self.num_fit = adapt_params["NUM_FIT"]
        self.num_off = adapt_params["NUM_OFF"]
        self.alpha = adapt_params["ALPHA"]
        self.omega = adapt_params["OMEGA"]
        self.gamma = adapt_params["GAMMA"]
        self.start = adapt_params["START"]

        self.arg = 0
        self.await_idx = 0

    def __call__(self, *args):
        if not self.on:
            pass
        else:
            self.update_learning_rate()
        return self.learning_rate

    def update_learning_rate(self):
        if len(self.optimizer_inst.cost_lst) > self.num_fit:
            # if len(self.optimizer_inst.cost_lst) > self.num_fit:
            #     logger.info(f"Variance: {np.var(np.asarray(self.optimizer_inst.cost_lst[-10:]))/10}")
            if self.await_idx == 0 and len(self.optimizer_inst.cost_lst) > self.start:
                pp = np.polyfit(
                    range(self.num_fit),
                    self.optimizer_inst.cost_lst[-self.num_fit:],
                    1,
                )
                slope = pp[0]
                if rank == 0:
                    logger.debug(f"Slope: {pp}")
                    logger.info(slope)

                if slope > 0:
                    fac = (
                        self.alpha
                        - np.arctan(self.arg / self.omega) * self.gamma * 2 / np.pi
                    )
                    # fac = self.alpha - self.gamma * special.erf(self.arg / self.omega)
                    if rank == 0:
                        logger.info(f"Factor decrease learning rate: {fac}")

                    self.learning_rate *= fac  # 0.5
                    self.await_idx = self.num_off  # 5
                    self.arg += 1
            else:
                if rank == 0:
                    logger.info("Awaiting new costs")
                self.await_idx = max(self.await_idx - 1, 0)


子函数17: operator.py

import numpy as np
import scipy


class Operator:
    # # 初始化函数 __init__(self, mat)
    def __init__(self, mat): # __init__ 是类的初始化方法，它接收一个参数mat。mat通常是一个矩阵，用于表示该操作符。
        self.op = mat  # 将传入的矩阵存储在实例的op属性中。
 
    # 获取期望值，该方法用于计算操作符在某个量子态上的期望值。
    def get_expectation_value(self, state):
        # state.get_amplitudes() 和 state.get_amplitudes_bra() 分别用于获取态的列矢量（态向量）
        # 和其转置复共轭（bra 向量），即量子力学中常用的 ket 和 bra 记号。@ 运算符表示矩阵乘法。
        # 具体计算流程为：bra 向量左乘操作符矩阵，再与 ket 向量相乘，得到的结果是一个复数。
        val = state.get_amplitudes_bra() @ self.op @ state.get_amplitudes()
        val = np.real(val) # 取结果的实部。期望值通常是实数，因此取实部可以确保结果符合物理意义。
        return val # 最终返回val，即期望值。

    @classmethod
    # 创建非线性空间表示1。这是一个类方法，用于根据给定的量子态 state 创建一个非线性空间表示的操作符。 
    def create_non_linear_space_rep(cls, state):
        # np.diag(state.get_prob_density())：将概率密度转换为对角矩阵，生成一个对角矩阵mat，其中对角元素是概率密度。
        mat = np.diag(state.get_prob_density()) # state.get_prob_density()应该返回态的概率密度（即态的模平方）。
        return Operator(mat) # 最后返回一个新的Operator实例，其对应的操作符矩阵为mat。

    @classmethod
    # 创建非线性空间表示2。这个方法类似于前面的create_non_linear_space_rep，但它使用了两个量子态state和state_pos_shift。
    def create_non_linear_space_rep_2(cls, state, state_pos_shift):
        # state.get_amplitudes()和state_pos_shift.get_amplitudes()分别返回两个量子态的幅值。
        # state.get_amplitudes()*state_pos_shift.get_amplitudes()是对应元素的乘积。np.diag(...)将乘积结果生成一个对角矩阵。
        mat = np.diag(state.get_amplitudes() * state_pos_shift.get_amplitudes())
        return Operator(mat) # 最后返回一个新的 Operator 实例。

    @classmethod
    # 创建带周期边界条件的拉普拉斯算子
    # 这个类方法用于创建一个带周期边界条件的拉普拉斯算子，通常用于描述系统的动能部分。
    def create_laplace_pbc(cls, num_ip):
        Ngrid = 2 ** num_ip # Ngrid=2 ** num_ip确定网格点的数量 Ngrid，是2的num_ip次幂。
        # np.linspace(-1, 1, Ngrid, endpoint=False, retstep=True) 在区间 [-1, 1) 上生成Ngrid个等距点，
        # 同时返回步长 dx。endpoint=False 表示不包含终点 1。
        x_coordinates, dx = np.linspace(-1, 1, Ngrid, endpoint=False, retstep=True)

        # 然后创建一个矩阵 kin：
        kin = (
                np.diag([-2] * Ngrid, 0) # np.diag([-2]*Ngrid, 0)生成对角元素为-2的矩阵，对应中心点。
                + np.diag([1] * (Ngrid - 1), 1) # np.diag([1]*(Ngrid-1),1)和np.diag([1]*(Ngrid-1), -1)分别生成上对角和下对角元素为1的矩阵。
                + np.diag([1] * (Ngrid - 1), -1) # 这些矩阵相加形成一个三对角矩阵，其中对角元素为-2，相邻的上、下对角元素为1。
        )
        # 接着处理周期边界条件：
        kin[0, -1] = 1 # kin[0, -1] = 1 设置第一行最后一列为 1，即在左端与右端连接。
        kin[-1, 0] = 1 # kin[-1, 0] = 1 设置最后一行第一列为 1，即在右端与左端连接。
        kin = kin / (dx**2) # 最后将整个矩阵除以 dx**2，得到最终的拉普拉斯算子矩阵。
        return Operator(kin) # 返回一个新的 Operator 实例，其操作符矩阵为 kin。
    


子函数18: statevector.py

import numpy as np
import scipy
import ast
import os

class Statevector:
    def __init__(self, amplitudes, x_coordinates=None, epsilon: float = 1e-3, ignore_norm: bool = False):
        self.epsilon = epsilon
        if not ignore_norm:
            self.check_normalization(amplitudes)
        if type(x_coordinates) == np.ndarray:
            self._x_coordinates = x_coordinates
            self._hn = self._x_coordinates[1] - self._x_coordinates[0]
        else:
            number_values = len(np.asarray(amplitudes))
            self._x_coordinates, self._hn = np.linspace(
                -1, 1, number_values, endpoint=False, retstep=True
            )
        self._n = int(np.log2(len(self._x_coordinates)))
        self._dim = self._x_coordinates.shape
        self._amplitudes = np.asarray(amplitudes)

    def check_normalization(self, amplitudes):
        norm = scipy.linalg.norm(amplitudes)
        if (norm - 1) > self.epsilon:
            raise ValueError(
                f"Wavefunction is not properly normalised, norm is: {norm}\nand amplitudes are {amplitudes}"
            )

    def get_x_coordinates(self):
        return self._x_coordinates

    def get_amplitudes(self):
        return self._amplitudes

    def get_amplitudes_bra(self):
        return np.conjugate(self._amplitudes).T

    def get_real_part(self):
        return np.real(self._amplitudes)

    def get_imag_part(self):
        return np.imag(self._amplitudes)

    def get_hn(self):
        return self._hn

    def to_interval(self):
        self._amplitudes = self._amplitudes / np.sqrt(self._hn)
        return self._amplitudes

    def get_prob_density(self):
        return np.power(np.abs(self._amplitudes), 2)

    def get_fidelity_with_target(self, target: "Wavefunction"):
        overlap = np.dot(np.conj(self._amplitudes), target.get_amplitudes())
        fidelity = np.power(np.abs(overlap), 2)
        return fidelity

    def get_overlap_with_target(self, target: "Wavefunction"):
        overlap = np.dot(np.conj(self._amplitudes), target.get_amplitudes())
        return overlap

    def create_projector_matrix(self):
        # Create |psi><psi| where |psi> = |V>
        projector_matrix = np.einsum("i,j", self._amplitudes, self._amplitudes)
        print("PROJECTOR", projector_matrix)
        return projector_matrix

    @classmethod
    def add(cls, wavefunc_1, wavefunc_2):
        if wavefunc_1.get_x_coordinates() != wavefunc_2.get_x_coordinates():
            raise ValueError(
                "Wavefunctions to be added are not defined on the same coordinate set"
            )
        return Statevector(
            wavefunc_1.get_amplitudes() + wavefunc_2.get_amplitudes(),
            wavefunc_1.get_x_coordinates(),
        )

    @classmethod
    def multiply(cls, wavefunc_1, factor):
        # print(np.linalg.norm(wavefunc_1.get_amplitudes()*factor))
        return Statevector(
            wavefunc_1.get_amplitudes() * factor,
            wavefunc_1.get_x_coordinates(),
        )

    @classmethod
    def init_plainwave(cls, ip_number, x_range: tuple = (-1, 1), k: float = np.pi):
        number_values = 2 ** ip_number
        x_coordinates = np.linspace(
            x_range[0], x_range[1], number_values, endpoint=False
        )
        amplitudes = [np.exp(1j * k * x) for x in x_coordinates]
        alpha = scipy.linalg.norm(amplitudes)  # Absolute was not there before
        amplitudes_normalised = np.asarray(amplitudes) / alpha
        return Statevector(amplitudes_normalised, x_coordinates)

    @classmethod
    def init_cos(cls, ip_number, x_range: tuple = (-1, 1), k: float = np.pi):
        number_values = 2 ** ip_number
        x_coordinates = np.linspace(
            x_range[0], x_range[1], number_values, endpoint=False
        )
        amplitudes = [np.cos(k * x) for x in x_coordinates]
        alpha = scipy.linalg.norm(amplitudes)  # Absolute was not there before
        amplitudes_normalised = np.asarray(amplitudes) / alpha
        return Statevector(amplitudes_normalised, x_coordinates)


    @classmethod
    def init_const(cls, ip_number, x_range: tuple = (-1, 1)):
        number_values = 2 ** ip_number
        x_coordinates = np.linspace(
            x_range[0], x_range[1], number_values, endpoint=False
        )
        amplitudes = [1 for x in x_coordinates]
        alpha = scipy.linalg.norm(amplitudes)  # Absolute was not there before
        # print("alpha", alpha)
        amplitudes_normalised = np.asarray(amplitudes) / alpha
        # print(scipy.linalg.norm(amplitudes_normalised))
        # print(amplitudes_normalised)
        return Statevector(amplitudes_normalised, x_coordinates)

    @classmethod
    def ho_groundstate(cls, ip_number, x_range: tuple = (-1, 1), omega: float = 7):
        number_values = 2 ** ip_number
        x_coordinates = np.linspace(
            x_range[0], x_range[1], number_values, endpoint=False
        )
        amplitudes = [
            np.power(omega / np.pi, 1 / 4) * np.exp(-omega / 2 * x ** 2)
            for x in x_coordinates
        ]
        alpha = scipy.linalg.norm(amplitudes)  # Absolute was not there before
        amplitudes_normalised = np.asarray(amplitudes) / alpha
        return Statevector(amplitudes_normalised, x_coordinates)

    @staticmethod
    def get_normalization_factor(amplitudes):
        norm = scipy.linalg.norm(amplitudes)
        return norm


class Potential(Statevector):
    def __init__(self, num_ip, x_range: tuple = (-1, 1), potential_dict: dict = None):
        self.kind = None
        self.alpha = None
        self.params = None
        self.off = False
        x_arr, amplitudes = self.construct(num_ip, x_range, potential_dict)
        super().__init__(amplitudes=amplitudes, x_coordinates=x_arr, ignore_norm=self.off)

    def get_potential(self):
        return self.alpha*self.get_amplitudes()

    def construct(self, num_ip, x_range, potential_dict):
        num_vals = 2 ** num_ip
        x_coordinates = np.linspace(
            x_range[0], x_range[1], num_vals, endpoint=False
        )
        len_int = (x_range[1]-x_range[0])
        if not potential_dict:
            raise ValueError("No potential dictionary given")

        self.kind = list(potential_dict.keys())[0]
        self.params = potential_dict[self.kind]
        self.off = (list(self.params.values())[0] == 0)
        if self.off:
            amplitudes = np.zeros(num_vals)
            self.alpha = 0
        else:
            if self.kind == "HARMONIC":
                amplitudes = np.array([1 / 2 * self.params["OMEGA"]**2 * x ** 2 for x in x_coordinates])

            elif self.kind == "BICHROMATIC":
                amplitudes = np.array([self.params["S1"]*np.sin(self.params["KAPPA1"]*(x+self.params["Offset"]*len_int)) +
                                       self.params["S2"]*np.sin(self.params["KAPPA2"]*(x+self.params["Offset"]*len_int))
                                       for x in x_coordinates])
                print("HERE in potential: ", amplitudes)

            else:
                raise ValueError(f"Type {self.kind} not implemented!")

            self.alpha = scipy.linalg.norm(amplitudes)
            amplitudes /= self.alpha
        return x_coordinates, amplitudes


# pot = "{'HARMONIC': {'OMEGA': 10}}"
# pot_dict = ast.literal_eval(pot)
# pot = Potential(4, potential_dict=pot_dict)
# print(pot.get_potential())
# print(pot.kind)



子函数19: parameters.py

import numpy as np


def get_param_mat(param_values):
    num_parameters = len(param_values)
    param_mat = np.vstack([param_values] * 2 * num_parameters)
    for i in range(num_parameters):
        param_mat[i, i] += np.pi / 2
        param_mat[(num_parameters + i), i] -= np.pi / 2  # Change back to Pi / 2

    return param_mat



子函数20: postprocess.py


import numpy as np
import itertools


def get_probability_array(counts, shots):
    num_qubits = len(list(counts.keys())[0])
    prob_arr = np.zeros(2**num_qubits)
    for j, binary_tuple in enumerate(itertools.product("01", repeat=num_qubits)):
        binary_str = "".join(binary_tuple)
        if binary_str in counts.keys():
            prob_arr[j] = counts[binary_str] / shots

    return prob_arr


def get_povm_variance(omega_arr, prob_arr, shots):
    omega_sq_exp = np.real(np.sum((omega_arr**2) * prob_arr))
    omega_exp_sq = np.real(np.sum(omega_arr * prob_arr)) ** 2
    var = np.sqrt(omega_sq_exp - omega_exp_sq) / shots

    return var


子函数21: tools_depr.py

import numpy as np
import itertools

# 这是一个函数的定义，函数名为 get_expectation_value_from_1d_counts，它接受一个参数 counts。
def get_expectation_value_from_1d_counts(counts):
    if "1" in counts.keys():   # 这里的 if 语句检查字典 counts 中是否包含键 "1"。
        shots = counts["0"] + counts["1"] # 如果counts中有键"1"，则计算总的测量次数shots，为"0"和"1"的出现次数之和。
        expectation_value = (1 * counts["0"] / shots) - (1 * counts["1"] / shots) # 计算期望值expectation_value
    else:
        shots = counts["0"] # 如果counts中没有键"1"，说明只有状态"0"。
        # 在这种情况下，期望值是 1 * counts["0"] / shots，结果为 1，因为所有的测量结果都是状态 "0"。
        expectation_value = 1 * counts["0"] / shots 
    return expectation_value # 函数返回计算出的期望值 expectation_value。


def get_statevector_from_ansatz(params, ansatz):
    ansatz.activate_save_statevector()
    ansatz.assign_parameters(params)
    state = ansatz.sim_statevector()
    return state


def get_param_mat(param_values):
    num_parameters = len(param_values)
    param_mat = np.vstack([param_values] * 2 * num_parameters)
    for i in range(num_parameters):
        param_mat[i, i] += np.pi / 2
        param_mat[(num_parameters + i), i] -= np.pi / 2  # Change back to Pi / 2

    return param_mat


def sigma_from_povm(counts, shots):
    possible_counts = counts.keys()
    sigma = 0
    for bit_str in possible_counts:
        if bit_str == "00":
            sigma += counts["00"] * 1
        elif bit_str == "01":
            sigma -= counts["01"] * 1
        elif bit_str == "10":
            pass
        else:
            sigma -= counts["11"] * 1
    return float(sigma / shots)


def get_probability_array(counts, shots):
    num_qubits = len(list(counts.keys())[0])
    prob_arr = np.zeros(2**num_qubits)
    for j, binary_tuple in enumerate(itertools.product("01", repeat=num_qubits)):
        binary_str = "".join(binary_tuple)
        if binary_str in counts.keys():
            prob_arr[j] = counts[binary_str] / shots

    return prob_arr


def compute_variance(omega_arr, prob_arr, shots):
    var = np.sqrt(
        np.real(
            (np.sum((omega_arr**2) * prob_arr) - (np.sum(omega_arr * prob_arr)) ** 2)
        )
        / shots
    )
    return var


def compute_generator_expectation_values(counts, shots):
    num_qubits = len(list(counts.keys())[0])
    k_i = np.zeros(num_qubits)
    k_ij = np.zeros((num_qubits, num_qubits))
    for i in range(num_qubits):
        for key in counts.keys():
            if key[-1 - i] == "0":
                # if key[i] == "0":
                k_i[i] += counts[key] / (shots * 2)
            else:
                k_i[i] -= counts[key] / (shots * 2)

    for i in range(num_qubits):
        for j in range(num_qubits):
            for key in counts.keys():
                if key[-1 - i] == key[-1 - j]:
                    # if key[i] == key[j]:
                    k_ij[i, j] += counts[key] / (shots * 4)
                else:
                    k_ij[i, j] -= counts[key] / (shots * 4)

    # print(k_i)
    # print(k_ij)
    return k_i, k_ij


子函数22: ansatz.py


# ansatz.py

import copy
import numpy as np
from qiskit import QuantumCircuit, QuantumRegister
from qiskit import Aer
simulator = Aer.get_backend("aer_simulator")
from qiskit.circuit import ParameterVector


class SU2:
    """
    变分量子电路的示例：单比特或多比特的交替 RY/RZ + CNOT。
    若 ip_number=1，则自动跳过 CNOT。
    """

    def __init__(
        self,
        ip_number,
        reps,
        layer_scheme="YC",
        include_barriers=True,
        entanglement="circular",
    ):
        self.registers = [QuantumRegister(ip_number)]
        self.ip_number = ip_number
        self.reps = reps
        self.scheme = layer_scheme
        self.include_barriers = include_barriers
        self.entanglement = entanglement

        self.qc = QuantumCircuit(*self.registers)
        # 预构造一些“自然梯度”块(可能在VQA中用到)，本示例不深入使用
        self.natgrad_blocks = self.construct_natgrad_blocks_2(ip_number, reps)
        # 正式构造 ansatz
        self.num_parameters, self.param_vector = self.setup_ansatz()
        self.num_layers = (len(layer_scheme) - 1) * (1 + reps)

    def setup_ansatz(self):
        # 计算参数数量：每个“非C”层对 ip_number 个量子比特都加旋转门
        num_parameters = (len(self.scheme) - 1) * self.ip_number * (self.reps + 1)
        layers_str = (self.scheme * (self.reps + 1))[:-1]
        param_vector = ParameterVector("SU2", num_parameters)

        count = 0
        for i, layer in enumerate(layers_str):
            if layer == "C":
                SU2.append_cnot_layer(self.qc, self.ip_number, self.entanglement)
                if self.include_barriers:
                    self.qc.barrier()
            else:
                params = param_vector[self.ip_number * count : self.ip_number * (count + 1)]
                SU2.append_r_layer(self.qc, params, layer)
                count += 1

        return num_parameters, param_vector

    def construct_natgrad_blocks_2(self, ip_number, reps):
        # 与上面 setup_ansatz 类似，只是细节略有不同，这里直接保留原逻辑
        layers_str = ("YC" * (reps + 1))[:-1]
        result = []
        qc = copy.deepcopy(self.qc)
        for i in range(1, len(layers_str) + 1):
            count = 0
            block_str = layers_str[:i]
            if block_str[-1] == "C":
                pass
            else:
                qc = copy.deepcopy(self.qc)
                num_rotation_layers = len(block_str.replace("C", "")) - 1
                param_vector = ParameterVector(
                    f"SU2_block", num_rotation_layers * ip_number
                )
                for j, layer in enumerate(block_str):
                    if layer == "C":
                        SU2.append_cnot_layer(qc, ip_number, self.entanglement)
                        qc.barrier()
                    else:
                        params = param_vector[
                            ip_number * count : ip_number * (count + 1)
                        ]
                        SU2.append_r_layer(qc, params, layer)
                        count += 1
                if block_str[-1] == "Y":
                    SU2.rotate_psi(qc, ip_number)
                result.append(qc)
        return result

    @staticmethod
    def append_r_layer(qc, params, layer):
        for i, param in enumerate(params):
            if layer == "Y":
                qc.ry(param, i)
            elif layer == "Z":
                qc.rz(param, i)
            else:
                raise ValueError("Layer not a rotation Y-/Z-Gate")

    @staticmethod
    def append_cnot_layer(qc, ip_number, entanglement):
        # 如果只有单比特，则跳过 CNOT
        if ip_number < 2:
            return

        cnot_circular = True
        if entanglement == "circular":
            if cnot_circular:
                qc.cnot(ip_number - 1, 0)
            for i in range(ip_number - 1):
                qc.cnot(i, (i + 1))
        elif entanglement == "full":
            for i in range(ip_number - 1):
                for j in range(i + 1, ip_number):
                    qc.cnot(i, j)
        elif entanglement == "rev":
            for i in range(ip_number - 2, 0):
                qc.cnot(i, i + 1)

    @staticmethod
    def rotate_psi(qc, ip_number):
        for i in range(ip_number):
            qc.sdg(i)
            qc.h(i)


子函数23: networks.py

from __future__ import annotations

import uuid

import matplotlib.pyplot as plt
import numpy as np
import datetime as dt

import scipy.linalg

from QM.statevector import Statevector, Potential
from QM.operator import Operator
from copy import deepcopy
from qiskit import QuantumRegister, ClassicalRegister, QuantumCircuit, circuit
from ansatz import SU2
from qiskit import Aer, transpile
from typing import Union
from pathlib import PurePath
from pathlib import Path
from mpslibrary import construct_mpd_circuit
from IO import get_logger
from measurement import MeasureHandler
from tools.tools_depr import compute_generator_expectation_values

# simulator = Aer.get_backend("aer_simulator_statevector") # Change 17.04
simulator = Aer.get_backend("aer_simulator")
FAIL = False
path = Path(__file__).parent.absolute()


logger = get_logger(__name__)


def initialise_registers_kinetic(main_number, anc_reset: bool = True):
    """
    This function initiales a quantum circuit with kinetic type structure,
    that means n main qubits, n-2 ancilla qubits as part of the QNPU and
    1 outer ancilla qubit
    Args:
        main_number: Number of qubits of Input Port
        anc_reset: If true, the n-2 ancilla qubit get
                   reset to |0>

    Returns:
        QuantumCircuit, List[Registers]
    """
    ancilla_cp = QuantumRegister(1, "ancCP")
    ancilla = QuantumRegister(main_number - 2, "ancQNPU")
    ip1 = QuantumRegister(main_number, "IP1")
    registers = [ancilla_cp, ancilla, ip1]
    qc = QuantumCircuit(*registers)
    qc.barrier()
    if anc_reset:
        for anc in ancilla:
            qc.reset(anc)
        for anc in ancilla_cp:
            qc.reset(anc)
    return qc, [ancilla_cp, ancilla, ip1]  # , cr]


def initialise_registers_potential(main_number, anc_reset: bool = True):
    ancilla_cp = QuantumRegister(1, "ancCP")
    ancilla = QuantumRegister(main_number, "ancQNPU")
    ip1 = QuantumRegister(main_number, "IP1")
    # cr = ClassicalRegister(1, "classical")
    registers = [ancilla_cp, ancilla, ip1]  # , cr]
    qc = QuantumCircuit(*registers)
    qc.barrier()
    if anc_reset:
        for anc in ancilla:
            qc.reset(anc)
        for anc in ancilla_cp:
            qc.reset(anc)
    return qc, [ancilla_cp, ancilla, ip1]  # , cr]


def initialise_registers_nonlinear_fake(main_number):
    ip1 = QuantumRegister(main_number, "IP1")
    registers = [ip1]  # , cr]
    qc = QuantumCircuit(*registers)
    qc.barrier()
    return qc, [ip1]  # , cr]


def initialise_registers_potential_qnpu(ip_number):
    anc = QuantumRegister(ip_number, name="ancillaQNPU")
    ip1 = QuantumRegister(ip_number, name="IP1")
    registers = [anc, ip1]
    qc = QuantumCircuit(*registers, name="QNPU V^ Gate")
    return qc, registers


def initialise_registers_non_linear(main_number, control_active: bool = True):
    ancilla_cp = QuantumRegister(1, "ancillaCP")
    ip1 = QuantumRegister(main_number, "IP1")
    ip2 = QuantumRegister(main_number, "IP2")
    ip3 = QuantumRegister(main_number, "IP3")
    cr = ClassicalRegister(1, "classical")
    registers = [ancilla_cp, ip1, ip2, ip3, cr]
    qc = QuantumCircuit(*registers)
    qc.barrier()
    if control_active:
        qc.h(ancilla_cp[0])
    return qc, [ancilla_cp, ip1, ip2, ip3, cr]


def append_inner_scheme_adder(qc, registers, main_number):
    layers = []
    ancilla, ip1 = registers[1:]  #  cr
    for i in range(2, main_number - 2):
        layers.append([i, i - 1])
    for layer in layers:
        qc.cx(ancilla[layer[1]], ip1[layer[0]])
        qc.ccx(ancilla[layer[1]], ip1[layer[0]], ancilla[layer[0]])


def append_end_scheme_adder(qc, registers, ip_number):
    ancilla_cp, ancilla, ip1 = registers  # , cr
    for i in range(1, ip_number - 2).__reversed__():
        qc.ccx(ancilla[i - 1], ip1[i], ancilla[i])
    qc.ccx(ancilla_cp[0], ip1[0], ancilla[0])


def append_adder_network(qc, registers, main_number, control_active: bool = True):
    """
    This function appends the controlled adder network to a kinetic type structure,
    that means n main qubits, n-2 ancilla qubits as part of the QNPU and 1 outer ancilla
    qubit and applies a Hadamard gate onto the outer ancilla, if control_active is True
    Args:
        qc: QuantumCircuit
        registers: List of Registers
        main_number: Number of qubits of the Input Port
        control_active: If true, apply Hadamard to outer
                        ancilla after c-Adder

    Returns:
        QuantumCircuit
    """
    ancilla_cp, ancilla, ip1 = registers  # , class_reg
    qc.cx(ancilla_cp[0], ip1[0])
    qc.ccx(ancilla_cp[0], ip1[0], ip1[1])
    qc.ccx(ancilla_cp[0], ip1[0], ancilla[0])
    qc.ccx(ip1[1], ancilla[0], ancilla[1])
    append_inner_scheme_adder(qc, registers, main_number)
    qc.cx(ancilla[-1], ip1[-2])
    qc.ccx(ancilla[-1], ip1[-2], ip1[-1])
    append_end_scheme_adder(qc, registers, main_number)
    return qc


def append_inner_scheme_adder_modular(qc, registers, num_ip):
    layers = []
    ancilla_reg, ip_reg = registers
    for i in range(2, num_ip - 2):
        layers.append([i, i - 1])

    for layer in layers:
        qc.cx(ancilla_reg[layer[1]], ip_reg[layer[0]])
        qc.ccx(ancilla_reg[layer[1]], ip_reg[layer[0]], ancilla_reg[layer[0]])


def append_end_scheme_adder_modular(qc, registers, num_ip):
    ancilla_reg, ip_reg = registers
    for i in range(1, num_ip - 2).__reversed__():
        qc.ccx(ancilla_reg[i - 1], ip_reg[i], ancilla_reg[i])
    qc.cx(ip_reg[0], ancilla_reg[0])


def build_adder_gate(num_ip, control=1):
    ancilla_reg = QuantumRegister(num_ip - 2, "ancQNPU")
    ip_reg = QuantumRegister(num_ip, "IP")
    registers = [ancilla_reg, ip_reg]
    qc = QuantumCircuit(*registers, name="Adder")
    qc.x(ip_reg[0])
    qc.cx(ip_reg[0], ip_reg[1])
    qc.cx(ip_reg[0], ancilla_reg[0])
    qc.ccx(ip_reg[1], ancilla_reg[0], ancilla_reg[1])
    append_inner_scheme_adder_modular(qc, registers, num_ip)
    qc.cx(ancilla_reg[-1], ip_reg[-2])
    qc.ccx(ancilla_reg[-1], ip_reg[-2], ip_reg[-1])
    append_end_scheme_adder_modular(qc, registers, num_ip)
    # print(qc)
    adder_gate = qc.to_gate().control(control)
    return adder_gate


def append_kinetic_network(nw_ansatz):
    nw_kinetic = Network.create_kinetic_network(nw_ansatz.IPs[0])
    nw = Network.add_networks(nw_ansatz, nw_kinetic, name="ansatz_with_kinetic_basic")
    return nw




class Network:
    """A class to represent a quantum network, it is getting subclassed
    by KineticNetwork, PotentialNetwork and NonlinearNetwork. It comprises
    the quantum circuit, the respective registers, the sizes of different
    Input Ports, the sizes of different ancilla sets and a dictionary for
    the parameter vector(s). When a quantum circuit is parametrised, these
    parameters are stored in an accompanying parameter vector. Initialising
    a Network with a parametrised quantum circuit, the parameter vector
    has to be stored into the 'the param_vectors_dict'. Thus, it is possible
    to assign / bind the parameters using the 'param_vectors_dict'. Multiple
    parameter vectors are also supported by the nature of a dictionary.
    """

    def __init__(
        self,
        qc,
        registers=None,
        ips=None,
        ancillas=None,
        param_vectors_dict: dict = {},
        name: str = uuid.uuid4(),
        type=None,
    ):
        self.qc = qc
        self.qc_assigned = None
        self.save_statevector = False
        self.registers = registers
        self.IPs = ips
        self.ancillas = ancillas
        if self.IPs:
            self.total_size = sum(self.ancillas + self.IPs)
        self.name = name
        self.pmv_dict = param_vectors_dict
        self.type = type
        self.measure_handler = MeasureHandler()
        self.meas_index = 0

    def __repr__(self):
        return f"--Network '{self.name}', #IP-qubits={self.IPs[0]}, #all-qubits={self.total_size}--"

    def get_values_params(self):
        if self.pmv_dict == {}:
            raise Exception("No parameters supplied!")
        else:
            return [value for value in self.pvm_dict[0]["values"]]

    def add_register(
        self, register: Union[QuantumRegister, QuantumCircuit, ClassicalRegister]
    ):
        self.qc.add_register(register)
        self.registers.append(register)

    def get_num_parameters(self):
        return self.qc.num_parameters

    def draw(self, folder: str = None, suffix: str = "", decompose: bool = False):
        filename = (
            PurePath(folder, f"{dt.date.today()}_{suffix}_dec_{decompose}.png")
            if folder
            else PurePath(
                path, "plots", f"{dt.date.today()}_{self.name}_dec_{decompose}.png"
            )
        )
        filename_trans = PurePath(str(filename)[:-4] + "_trans.png")

        if decompose:
            self.qc.decompose().draw(
                output="mpl", style={"fontsize": 22}, filename=filename
            )
        else:
            plt.figure(figsize=(14, 10))
            self.qc.draw(output="mpl", style={"fontsize": 22}, filename=filename)
            plt.tight_layout()
            plt.savefig(filename_trans, transparent=True, dpi=600)

    def draw_assigned(
        self, folder: str = None, suffix: str = "", decompose: bool = False
    ):
        filename = (
            PurePath(folder, f"{dt.date.today()}_{suffix}_dec_{decompose}.png")
            if folder
            else PurePath(
                path, "plots", f"{dt.date.today()}_{self.name}_dec_{decompose}.png"
            )
        )
        self.qc_assigned.decompose().draw(
            output="mpl", style={"backgroundcolor": "#EEEEEE"}, filename=filename
        )

    def set_measure_index(self, val):
        self.meas_index = val

    def increment_measure_index(self):
        self.meas_index += 1

    def assign_parameters(self, param_values, index: int = 0):
        self.qc_assigned = self.qc.assign_parameters(
            {self.pmv_dict[index]["ParamVector"]: param_values}
        )
        self.pmv_dict[index]["values"] = param_values

    def simulate(self):
        # TODO: What happens now when I removed the default ClassicalRegister? This simulate will fail
        if self.qc_assigned:
            t_circ = transpile(self.qc_assigned, simulator)
        else:
            t_circ = transpile(self.qc, simulator)
        result = simulator.run(t_circ).result()
        return result, t_circ

    def estimate_sigma(self, shots: int = 1024):
        sigma = self.measure_handler.estimate_sigma_ancilla(self, shots)
        return sigma

    def activate_save_statevector(self):
        self.qc.save_statevector()
        self.save_statevector = True

    def simulate_state_vector(self):
        if not self.save_statevector:
            raise ValueError("Statevector is not saved at end of circuit")

        if self.qc_assigned:
            t_circ = transpile(self.qc_assigned, simulator)
        else:
            t_circ = transpile(self.qc, simulator)
        result = simulator.run(t_circ).result()
        statevector = Statevector(result.get_statevector(t_circ))
        return statevector

    @classmethod
    def create_non_linear_network(cls, ip_number, control_active: bool = True):
        qc, registers = initialise_registers_non_linear(
            ip_number, control_active=control_active
        )
        return Network(qc, registers, [ip_number, ip_number, ip_number], [1])





class Ansatz(Network):    # 定义继承network类。可以使用network中的属性和方法
    def __init__(
        self,
        num_ip,
        reps,
        layer_scheme="YC",    #描述层的结构类型
        ansatz_type="SU2",    # 类型设置为su2,决定使用何种构造方案。
        include_barriers=False,   # 是否在量子层中加入障碍，可以帮助调试和查看电路结构默认值为false
        entanglement="circular",   # 量子比特的纠缠类型 circular表示循环结构纠缠
    ):
        if ansatz_type == "SU2":     # 创建su2电路
            ansatz_su2 = SU2(         # 使用su2类，创建了一个ansatz_su2的实例
                num_ip, reps, layer_scheme, include_barriers, entanglement   # 传递参数num_ip, reps, layer_scheme, include_barriers, entanglement 
            )  # Set entanglement     # 这个 ansatz_su2 变量会作为ansatz类的基础电路结构。
               # 参数向量和参数字典
            param_vector = ansatz_su2.param_vector  # param_vector：获取ansat_su2的参数向量，用于后续对参数的优化
            pmv_dict = {                          # 创建了一个参数向量字典，里边包含一个索引0，表示初始参数配置
                0: {
                    "ParamVector": param_vector,    # ParamVector：参数向量
                    "values": np.ones(ansatz_su2.num_parameters),   #  values：参数的初始值。所有值初始化为1.
                }
            }
            super().__init__(        # 调用父类（network）的构造函数。
                ansatz_su2.qc,      # 量子电路对象，
                ansatz_su2.registers,  # 量子寄存器
                [num_ip],     #输入的量子比特数量
                [],       # 表示没有额外的输出量子比特。
                param_vectors_dict=pmv_dict,  #参数向量的字典，提供了参数配置
                name="SU2 Ansatz Network",    # 设置电路的名称为SU2 Ansatz Network
            )     
               # 其他配置
            self.save_statevector = False      # 设置是否保存状态向量，当前为false，再调试和测试时比较有用。
            self.natgrad_blocks = ansatz_su2.natgrad_blocks   #继承了ansatz_su2的自然梯度块（natgrad_blocks），用于后续优化中的自然梯度计算。
            self.num_layers = ansatz_su2.num_layers       # 保存了层的数量，表示该ansatz中有多少层
    # 332-364行代码，这段代码主要功能通过传递参数构建一个量子电路ansatz对象。它使用了一个基于su2类的电路实例，随后又调用了父类的构造函数来完成初始化，
    # 并且为电路添加了一些额外的属性（比如参数向量字典； 自然梯度块）

    def embed(self, term):   # 定义了一个embed方法。它主要用于嵌入不同类型的寄存器
        ip_number = self.IPs[0]   # 从self.IPs中取出第一个值作为量子比特的数量。ip_number决定了寄存器的大小。
         # 针对k的嵌入
        if term == "K":       # 如果term是k，则执行以下代码。
            qc, registers = initialise_registers_kinetic(ip_number)  #调用initialise_registers_kinetic函数初始化一个“动能”的量子电路和寄存器
            qc.compose(         # qc.compose():将当前对象的量子电路self.qc组合（合并）到刚才创建的qc中
                self.qc, qubits=range(ip_number - 1, 2 * ip_number - 1), inplace=True  
             # qubits=range(ip_number - 1, 2 * ip_number - 1)：表示合并到量子比特索引范围。inplace=True：表示组合结果是直接应用到qc中，不再创建新的对象。
            )
            # 调用父类构造函数，初始化新嵌入的电路
            super().__init__( 
                qc,       # 使用刚才组合后的量子电路
                registers,  # 使用初始化后的寄存器
                [ip_number],  # 表示输入寄存器的数量
                [1, ip_number - 2],   # 表示输出寄存器的数量和索引范围
                param_vectors_dict=self.pmv_dict,  # 使用类中的参数向量字典elf.pmv_dict
                name=f"ansatz_on_{term}_network",   # 为嵌入的网络设置名称，名称中包括term值。
            )
         
         # 针对v的嵌入
        elif term == "V":   # 如果term是v，表示要嵌入势能寄存器
            qc, registers = initialise_registers_potential(ip_number)  #调用initialise_registers_potential函数初始化一个“势能”的量子电路和寄存器
            qc.compose(     # qc.compose():将当前对象的量子电路self.qc组合（合并）到刚才创建的qc中
                self.qc, qubits=range(ip_number + 1, 2 * ip_number + 1), inplace=True
            # qubits=range(ip_number + 1, 2 * ip_number + 1)：表示不同的量子比特索引范围。inplace=True：表示组合结果是直接应用到qc中，不再创建新的对象。
            )
            super().__init__(
                qc,
                registers,
                [ip_number],
                [1, ip_number],
                param_vectors_dict=self.pmv_dict,
                name=f"ansatz_on_{term}_network",  # 表示该电路是针对v进行的嵌入
            )
            # 针对i的嵌入
        elif term == "I":
            qc, registers = initialise_registers_nonlinear_fake(ip_number)
            qc.compose(self.qc, qubits=range(0, ip_number), inplace=True)
            super().__init__(
                qc,
                registers,
                [ip_number],
                [0, 0],   # 表示输出寄存器的数量和索引的范围均为0，表示该网络仅用作中间处理。
                param_vectors_dict=self.pmv_dict,
                name=f"ansatz_on_{term}_network",
            )

    def construct_blockdiag_fubini_study_metric_tensor(self, params):   # 整体用作于构造fubini_study度量张量
        shots = int(1e4)     # 设置了测量的次数10000次
        num_ip = self.IPs[0]   # 获取量子比特数量
        gs = []      # 初始化一个空列表gs，它主要用于存储fubini_study度量张量
        # print(params)
        for qc_block in self.natgrad_blocks:    #遍历self.natgrad_blocks中每个块，这些块是梯度计算中用到的量子电路块
            # print(params[: qc_block.num_parameters])
            # params[: qc_block.num_parameters]：指定qc_block的参数为输入的params
            qc_block = qc_block.assign_parameters(
                params[: qc_block.num_parameters], inplace=False     # inplace=False：指的是不在原对象上修改，而是生成了新的量子电路对象
            )
            # print(qc_block)

            logger.debug(qc_block)    # debug用于调试打印qc_block的信息
            counts = MeasureHandler.measure_all(qc_block, shots=shots)  # 通过MeasureHandler对qc_block进行测量，执行shots多少次的测量操作，结果保存在counts中。
            k_i, k_ij = compute_generator_expectation_values(counts, shots) # 使用测量结果counts和测量次数shots，计算生成器的期望值，返回两个值k_i, k_ij
            gs.append(np.array(k_ij - np.outer(k_i, k_i))) # 将计算得到的fubini_study度量张量结果是(k_ij - np.outer(k_i, k_i))以nupy数组的形式添加到列表gs中
                                                           # 这里的计算是为了得到张量形式通过内积的差值来表示
        fsmt = np.real(scipy.linalg.block_diag(*gs))   # 使用scipy的块对角函数，构建fubini_study度量张量块对角矩阵
        # np.testing.assert_array_equal(FSMT, FSMT2)
        return fsmt    # 返回计算得到后的矩阵

    def get_state_vector(self, params):   # 这个函数用于获取当前量子态的向量
        if not self.save_statevector:      # 检查是否需要保存状态向量，如果不是，则激活保存状态向量的选项
            self.activate_save_statevector()  
        self.assign_parameters(params)     # 使用给定的参数作为电路分配参数
        state = self.simulate_state_vector()    # 进行量子态向量的模拟
        return state  # 返回模拟的量子态


class KineticNetwork(Network):
    def __init__(self, qc, registers, num_ip, ansatz=None):
        super().__init__(
            qc,
            registers,
            [num_ip],
            [1, num_ip - 2],
            name="Kinetic Network",
            type="K",
        )
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz
        self.h_n = 2 / (2**num_ip)
        self.rescaling = -1 / (self.h_n**2)
        self.energy = None

    # def prepend_ansatz_state(self, params):
    #     print(params)
    #     state = self.ansatz.get_prepared_state(params)
    #     print(self.qc)
    #     self.qc_assigned, registers = initialise_registers_kinetic(self.IPs[0])
    #     gate = StatePreparation(state.get_amplitudes(), label="Ansatz")
    #     a = self.qc_assigned.compose(gate, qubits=[3, 4, 5, 6])
    #     print(a)

    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):
            self.assign_parameters(params)

        self.set_measure_index(0)
        sigma = self.measure_handler.estimate_sigma_ancilla(self, shots)
        self.energy = (1 - sigma) / (self.h_n**2)
        return self.energy

    def get_nth_gradient_component(self, params, shots, nth):
        params[nth] += np.pi / 2
        self.assign_parameters(params)
        sigma_ps = self.estimate_sigma(shots)
        self.increment_measure_index()

        params[nth] -= np.pi
        self.assign_parameters(params)
        sigma_ns = self.estimate_sigma(shots)
        self.increment_measure_index()

        res = self.rescaling * (sigma_ps - sigma_ns) * 1 / 2
        params[nth] += np.pi / 2
        return res

    @classmethod
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs):
        qc, registers = KineticNetwork.initialise_registers(num_ip, anc_reset=True)
        if ansatz:
            ansatz = deepcopy(ansatz)
            gate = ansatz.qc.to_gate(label="Ansatz")
            qc.compose(gate, qubits=range(num_ip - 1, 2 * num_ip - 1), inplace=True)
            qc.barrier()

        if hadamard:
            qc.h(registers[0][0])
        adder_gate = KineticNetwork.build_adder_gate(num_ip, control=1)
        qc.compose(adder_gate, qubits=range(0, 2 * num_ip - 1), inplace=True)
        if hadamard:
            qc.h(registers[0][0])
        return KineticNetwork(qc, registers, num_ip, ansatz=ansatz)

    @staticmethod
    def initialise_registers(num_ip, anc_reset: bool = True):
        """
        This function initiales a quantum circuit with kinetic type structure,
        that means n main qubits, n-2 ancilla qubits as part of the QNPU and
        1 outer ancilla qubit
        Args:
            num_ip: Number of qubits of Input Port
            anc_reset: If true, the n-2 ancilla qubit get
                       reset to |0>

        Returns:
            QuantumCircuit, List[Registers]
        """
        ancilla_cp = QuantumRegister(1, "ancCP")
        ancilla = QuantumRegister(num_ip - 2, "ancQNPU")
        ip1 = QuantumRegister(num_ip, "IP1")
        registers = [ancilla_cp, ancilla, ip1]
        qc = QuantumCircuit(*registers)
        qc.barrier()
        if anc_reset:
            for anc in ancilla:
                qc.reset(anc)
            for anc in ancilla_cp:
                qc.reset(anc)
        return qc, [ancilla_cp, ancilla, ip1]

    @staticmethod
    def append_inner_scheme_adder(qc, registers, num_ip):
        layers = []
        ancilla_reg, ip_reg = registers
        for i in range(2, num_ip - 2):
            layers.append([i, i - 1])

        for layer in layers:
            qc.cx(ancilla_reg[layer[1]], ip_reg[layer[0]])
            qc.ccx(ancilla_reg[layer[1]], ip_reg[layer[0]], ancilla_reg[layer[0]])

    @staticmethod
    def append_end_scheme_adder(qc, registers, num_ip):
        ancilla_reg, ip_reg = registers
        for i in range(1, num_ip - 2).__reversed__():
            qc.ccx(ancilla_reg[i - 1], ip_reg[i], ancilla_reg[i])
        qc.cx(ip_reg[0], ancilla_reg[0])

    @staticmethod
    def build_adder_gate(num_ip, control=1):
        ancilla_reg = QuantumRegister(num_ip - 2, "ancQNPU")
        ip_reg = QuantumRegister(num_ip, "IP")
        registers = [ancilla_reg, ip_reg]
        qc = QuantumCircuit(*registers, name="Adder")
        qc.x(ip_reg[0])
        qc.cx(ip_reg[0], ip_reg[1])
        qc.cx(ip_reg[0], ancilla_reg[0])
        qc.ccx(ip_reg[1], ancilla_reg[0], ancilla_reg[1])
        KineticNetwork.append_inner_scheme_adder(qc, registers, num_ip)
        qc.cx(ancilla_reg[-1], ip_reg[-2])
        qc.ccx(ancilla_reg[-1], ip_reg[-2], ip_reg[-1])
        KineticNetwork.append_end_scheme_adder(qc, registers, num_ip)
        adder_gate = qc.to_gate().control(control)
        return adder_gate


# ansatz = Ansatz(4, 2, include_barriers=False)
# kin = KineticNetwork.construct(4, ansatz=ansatz)
# print(kin.qc)
# a = np.ones(4 * 3)


class PotentialNetwork(Network): # 定义PotentialNetwork类，继承Network类
     # qc 是量子电路对象，registers：量子寄存器，num_ip：量子比特数量，potential：表示势能的对象，ansatz：表示一个变分量子特征态对象，默认值为None
    def __init__(self, qc, registers, num_ip, potential, ansatz=None):   # 这是PotentialNetwork类的构造函数，用于初始化对象，它由5个参数
        # super().__init__(）：调用父类Network的构造函数，并且传递了一些初始化参数，
        # qc 是量子电路对象，registers：量子寄存器，num_ip：量子比特数量，
        super().__init__(      
            qc,              
            registers,
            [num_ip],
            [1, num_ip],  #用于某些初始化的量子比特的索引范围
            name="PotentialNetwork",    # 指定网络的名称为：PotentialNetwork。这是变分量子特征态。
            type="V",     # 表示势能网络。
        )
        # 595-597的代码： 如果传递了ansatz参数，则将ansatz中的参数字典pmv_dict赋值给当前对象的属性pmv_dict，并且将ansatz保存到对象属性中。
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz

        self.potential = potential    # 将传入的potential赋值给对象属性self.potential，这个属性用于存储势能信息。
        self.rescaling = self.potential.alpha  # 获取potential的alpha属性，存储到self.rescaling中。
        self.energy = None    # 将self.energy初始化为one，方便后续计算当中存储能量值
    # 定义了compute_cost方法，用于计算网络损失函数或代价。它由两个参数：params是一个np.数组，它是表示需要分配的参数。shots：用于量子测量的采样次数，默认值为1024
    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):  # 检查params是否为np的数组或者是列表的类型，如果是则会调用assign_parameters(params)方法，
            self.assign_parameters(params)          # 然后将参数分配给量子电路。用于设置量子电路中可调节的变分量子参数。

        self.set_measure_index(0)       # 调用set_measure_index(0)方法，将测量的索引设为0.用于指定测量的目标，或者是初始化测量的顺序。
        # 通过self.measure_handler.estimate_sigma_ancilla来计算sigma。measure_handler：负责测量的对象, 它这里使用辅助量子比特ancilla进行测量，并返回了
        # 了一个值sigma，代表某种度量或者测量的结果。
        sigma = self.measure_handler.estimate_sigma_ancilla(self, shots)  
        # 将sigma值乘以self.rescaling，并且将结果存储到self.energy。rescaling:缩放因子，用于对测量结果sigma概率统计一个最终的能量经过缩放后的测量值。
        self.energy = self.rescaling * sigma  
        return self.energy     # 返回计算得到的当前状态下的self.energy能量值。能量                 
    
    # 定义方法get_nth_gradient_component，用于计算给定参数的第nth个分量。params：参数向量，表示要调整的量子电路参数，
    # shots：测量次数，它是表示量子计算中为了获得可靠结果所进行的重复次数。nth：指定要计算的梯度的参数索引。
    def get_nth_gradient_component(self, params, shots, nth):  
        params[nth] += np.pi / 2  # 对params中的索引为nth的参数增加pi/2。
        self.assign_parameters(params)    # 将更新后的参数分配到量子电路中，通过调整参数使量子电路的状态进行变化，从而进行后续测量。
        # 使用当前参数配置来执行量子测量，获取测量值sigma_ps，ps：正向偏移。estimate_sigma：这个方法进行多次的量子测量返回结果。
        sigma_ps = self.estimate_sigma(shots)  
        self.increment_measure_index()  # 增加测量的索引，主要用于记录或者是标记当前测量步骤。

        params[nth] -= np.pi     # 将params中的索引为nth的参数减少pi。此时，相当于在初始的位置的基础上减去pi/2，用于梯度的负方向。
        self.assign_parameters(params)   # 再次将调整后的参数，再将更新后的参数分配到量子电路中，通过调整参数使量子电路的状态进行变化，从而进行后续测量。
        sigma_ns = self.estimate_sigma(shots)   # 使用当前参数执行测量，获得测量值sigma_ns。ns：负方向测量
        self.increment_measure_index()   # 再次增加测量索引，继续跟踪测量的步骤。

        # 根据量子测量结果计算梯度值。sigma_ps - sigma_ns：是基于差分方法估计梯度，乘以1/2进行归一化。self.rescaling：是缩放因子，对结果进行适当调整。
        res = self.rescaling * (sigma_ps - sigma_ns) * 1 / 2  
        params[nth] += np.pi / 2   # ↩恢复params中的第nth个参数的值，使它回到初始状态，这是为了不影响后续的梯度计算或者是其他操作
        return res  # 返回计算后的梯度值res

    @classmethod     # 类方法：它的意思通过类本身调用不需要实例化，
        # 定义类方法construct，参数包括：输入的量子比特数num_ip，是否加入hadamard门布尔值，量子态准备网络ansatz和任意数量的其他参数**kwargs
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs): 
        # 用于输入量子比特数num_ip和字典potential_dict，它会来创建一个potential对象
        potential = Potential(num_ip=num_ip, potential_dict=kwargs["POTENTIAL"])

        if potential.off:   # 检查potential对象是否并禁用
            # 如果这个potential被禁用则返回一个由QuantumCircuit(1)构建一个空的量子网络，
            return PotentialNetwork(qc=QuantumCircuit(1),  registers=[], num_ip=1, potential=potential)
        
        # 它会调用这个PotentialNetwork类方法initialise_registers初始化量子电路和寄存器。输入量子比特数为um_ip并设置辅助量子比特为重置状态。
        qc, registers = PotentialNetwork.initialise_registers(num_ip, anc_reset=True)
        if ansatz:    # 如果提供了ansatz参数，
            ansatz = deepcopy(ansatz)    # 对ansatz进行深拷贝，防止修改原始对象，
            gate = ansatz.qc.to_gate(label="Ansatz")    # 将ansatz对象中的量子电路qc转化为一个量子门，标记为Ansatz
            qc.compose(gate, qubits=range(num_ip+1, 2 * num_ip + 1), inplace=True)  # 它会将生成的量子门gate添加到当前的量子电路qc上，用于指定量子比特
            qc.barrier()     # 在电路中添加屏障，防止量子门被修改。

        if hadamard:      # 如果这个adamard参数为True真，则应用hadamard门
            qc.h(registers[0][0])   # 在第一个寄存器的第一个量子比特上应用hadamard门

        # a_ks = [0, 0, 1 / 2 * omega**2]
        # potential_gate, alpha, potential = PotentialNetwork.build_harmonic_osc_gate(
        #     num_ip, a_ks=a_ks, num_control=1
        # )
        # 调用这个类PotentialNetwork类方法build_potential_gate，创建一个与势能相关的量子门，输入参数量子比特数量num_ip，势能对象potential，
        # num_control=1：指定控制量子比特的数量。
        potential_gate = PotentialNetwork.build_potential_gate(num_ip, potential, num_control=1)

        # 将potential_gate，它会加入到当前的量子电路中，qc.compose：它是将新的量子门添加到已经有的量子门中，potential_gate：要加入到量子门对象
        # qubits=range(0, 2 * num_ip + 1)：指定量子门的作用，量子比特的一个索引范围，从0到2*num_ip+1，inplace：表示现有的量子电路qc直接修改
        # 不生成新的量子电路。
        qc.compose(potential_gate, qubits=range(0, 2 * num_ip + 1), inplace=True)
        if hadamard:   # 如果这个adamard参数为True真，则应用hadamard门
            qc.h(registers[0][0])   # 在第一个寄存器的第一个量子比特上应用hadamard门
        # 返回了一个PotentialNetwork实例，该实例包含构建好的量子电路qc，
        return PotentialNetwork(
            qc, registers, num_ip, potential, ansatz=ansatz  # qc：完成了组合势能门和可能的hadamard门的量子电路
        )

    @staticmethod # 静态方法，用来构建与势能相关的量子门
    # num_ip：量子比特数量，potential：与势能相关的对象，num_control=1：控制量子比特的数量，默认为1.
    def build_potential_gate(num_ip, potential, num_control=1): 
        # 创建两个量子寄存器。
        anc = QuantumRegister(num_ip, name="ancQNPU")  # 创建了ancQNPU寄存器，它包含num_ip个量子比特，主要是用于辅助函数anc
        ip1 = QuantumRegister(num_ip, name="IP1")  # 创建了IP1寄存器，它包含num_ip个量子比特，表示主要的输入寄存器
        registers = [anc, ip1]  # 将创建的寄存器存储在一个列表中
        # 使用寄存器创建一个量子电路，QuantumCircuit(*registers)：解包寄存器列表，将anc和ip1作为参数传入，然后创建量子电路。给该电路命名为：V^ Gate
        qc = QuantumCircuit(*registers, name="V^ Gate")   
        # qc_mpd, alpha, potential = construct_mpd_circuit(n=num_ip, a_ks=a_ks)
        # circuit.library.StatePreparation：从qiskit中去导入量子态准备的门。potential.get_amplitudes：获取势能的振幅用于初始化状态。
        gate_init = circuit.library.StatePreparation(potential.get_amplitudes()) 
        # 将初始化的状态准备门gate_init组合的量子网络qc中，qc.compose：这个就是将门或者是电路组合到现有的电路中，gate_init：需要组合的量子门。
        # qubits=range(0, num_ip)：指定量子门作用量子比特从0到num_ip。inplace=True：在现有的量子电路中直接进行修改。
        qc.compose(gate_init, qubits=range(0, num_ip), inplace=True)

        # 691-692: 在辅助量子比特和输入量子比特之间应用cnot量子门。
        for qbit_anc, qbit_ip in zip(registers[0], registers[1]): # 将辅助的寄存器和输入的寄存器当中的量子比特配对遍历
            qc.cnot(qbit_ip, qbit_anc)     # 再输入量子比特qbit_ip和辅助量子比特qbit_anc之间应用cnot门
        controlled_v = qc.to_gate().control(num_control)  # 将构建好的量子电路转化为受控门controlled_v，受控量子比特数量是num_control。
        return controlled_v # 返回构建的受控量子门。

    @staticmethod # 静态方法，通过类和别的函数都可以调用
    def build_harmoninc_osc_gate(num_ip, a_ks, num_control=1):  # 定义方法，num_ip：量子比特数量，a_ks：一些参数，num_control=1：用于控制门的数量，默认是1
        # 698-699创建量子寄存器，anc 和ip1是两个量子寄存器，各包含num_ip量子比特数，anc被命名为ancQNPU，ip1被命名为IP1。
        anc = QuantumRegister(num_ip, name="ancQNPU")  
        ip1 = QuantumRegister(num_ip, name="IP1")  
        registers = [anc, ip1]     # 创建量子电路，把两个寄存器组合成一个列表registers
        qc = QuantumCircuit(*registers, name="V^ Gate")   # 使用这些寄存器来构建一个新的量子电路qc，然后给它命名为V^ Gate。
        # 调用了construct_mpd_circuit的参数，来构造一个多参数驱动mpd电路，这个参数返回了qc_mpd量子电路，alpha和potential就是后续相关的参数。
        qc_mpd, alpha, potential = construct_mpd_circuit(n=num_ip, a_ks=a_ks) 
        # 将生产的qc_mpd电路合成到qc，作用在量子比特范围是0到num_ip，inplace=True：表示直接在原量子电路上修改。
        qc.compose(qc_mpd, qubits=range(0, num_ip), inplace=True)  
        # 708-709: 将辅助寄存器和输入寄存器中的量子比特一一配对，并且执行了cnot门的操作。它的控制量子比特为qbit_ip，目标量子比特为qbit_anc
        for qbit_anc, qbit_ip in zip(registers[0], registers[1]):  
            qc.cnot(qbit_ip, qbit_anc)
        controlled_v = qc.to_gate().control(num_control) # 将整个量子电路qc转化为一个门操作，并且为它添加控制量子比特，控制数量为num_control，返回711行
        return controlled_v, alpha, potential   # 返回生产的受控门以及alpha，势能。

    @staticmethod # 静态方法，初始化量子寄存器，
    # 定义一个方法initialise_registers，接受两个参数，一个量子比特数量num_ip，一个anc_reset=True：控制辅助寄存器是否需要重置，默认为True
    def initialise_registers(num_ip, anc_reset=True): 
        # 717-718: 创建辅助量子寄存器，717:创建一个包含一个量子比特的量子寄存器名称为ancilla_cp。718:创建了一个包含num_ip个的量子比特的量子寄存器名称为ancQNPU。
        ancilla_cp = QuantumRegister(1, "ancCP")   
        ancilla = QuantumRegister(num_ip, "ancQNPU")
        ip1 = QuantumRegister(num_ip, "IP1")   # 创建了一个包含num_ip个量子比特的量子寄存器，名称为IP1。
        registers = [ancilla_cp, ancilla, ip1]  # 将所有的寄存器存储到列表registers中。
        qc = QuantumCircuit(*registers)   # 使用registers中所有的寄存器来创建一个量子电路qc。
        qc.barrier()   # 在量子电路总添加一个屏障。它会去用于阻止量子门之间的优化确保量子门执行顺序。
        if anc_reset:    # 如果anc_reset为True的话，执行下边的量子寄存器重置操作。
            for anc in ancilla:  # 遍历辅助寄存器ancilla中的每个量子比特，并且调用qc.reset(anc)来重置它们。
                qc.reset(anc)
            for anc in ancilla_cp:   # 遍历ancilla_cp中的量子比特并重置它们。
                qc.reset(anc)
        return qc, [ancilla_cp, ancilla, ip1]  # 返回了构建好的量子电路qc，以及包含ancilla_cp, ancilla, ip1的寄存器列表。


# ansatz = Ansatz(4, 2, include_barriers=False)
# pot = PotentialNetwork.construct(4, **{"OMEGA_HO": 2}, ansatz=ansatz)
# print(pot.qc)

class NonLinearNetwork(Network):
    def __init__(self, qc, registers, num_ip, g, ansatz=None):
        super().__init__(
            qc,
            registers,
            [num_ip],
            [0, 0],
            name="Non-linear network",
            type="I",
        )
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz

        self.activate_save_statevector()
        self.h_n = 2 / (2**num_ip)
        self.g = g
        self.rescaling = 1 / 2 * self.g / self.h_n
        self.energy = None

    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):
            self.assign_parameters(params)

        self.set_measure_index(0)
        state_current = self.simulate_state_vector()
        op = Operator.create_non_linear_space_rep(state_current)
        exp_value = op.get_expectation_value(state_current)
        self.energy = self.rescaling * exp_value
        return self.energy

    def get_nth_gradient_component(self, params, shots, nth):
        self.assign_parameters(params)
        state_current = self.simulate_state_vector()
        op_current = Operator.create_non_linear_space_rep(state_current)

        params[nth] += np.pi / 2
        self.assign_parameters(params)
        state_pos_shift = self.simulate_state_vector()  # Positive shift
        sigma_pos_shift = op_current.get_expectation_value(state_pos_shift)
        self.increment_measure_index()

        params[nth] -= np.pi
        self.assign_parameters(params)
        state_neg_shift = self.simulate_state_vector()  # Negative shift
        sigma_neg_shift = op_current.get_expectation_value(state_neg_shift)

        params[nth] += 3*np.pi / 2
        # params[nth] += np.pi
        self.assign_parameters(params)
        STATE_PI_SHIFT = self.simulate_state_vector()  # PI SHIFT

        op_ps = Operator.create_non_linear_space_rep_2(state_current, STATE_PI_SHIFT)
        sigma_op_pos_shift = op_ps.get_expectation_value(state_current)
        # logger.info(f"PARAM PI/2 shift: {self.rescaling * (sigma_pos_shift - sigma_neg_shift) * 1 / 2}")
        # logger.info(f"PI POS SHIFT: {self.rescaling * sigma_op_pos_shift}")
        res = self.rescaling * (
            (sigma_pos_shift - sigma_neg_shift) * 1 / 2
            + sigma_op_pos_shift * 2 * 1/2
        )
        params[nth] -= np.pi
        return res

    @classmethod
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs):
        g = kwargs["G"]
        if g == 0:
            return NonLinearNetwork(qc=QuantumCircuit(1), registers=[], num_ip=1, g=g)

        qc, registers = NonLinearNetwork.initialise_registers_fake(num_ip)
        if ansatz:
            ansatz = deepcopy(ansatz)
            gate = ansatz.qc.to_gate(label="Ansatz")
            qc.compose(gate, inplace=True)

        # if hadamard:
        #     qc.h(registers[0][0])

        # NonLinearNetwork.build_gate
        # qc.compose(nonlinear gate, qubits=range(0, 2 * num_ip + 1), inplace=True)
        # if hadamard:
        #     qc.h(registers[0][0])
        return NonLinearNetwork(
            qc, registers, num_ip, g, ansatz=ansatz
        )

    @staticmethod
    def initialise_registers_fake(num_ip):
        ip1 = QuantumRegister(num_ip, "IP1")
        registers = [ip1]
        qc = QuantumCircuit(*registers)
        qc.barrier()
        return qc, [ip1]


class OverlapNetwork(Network):
    def __init__(self, qc, registers, num_ip, ansatz=None):
        super().__init__(
            qc,
            registers,
            [num_ip],
            [0, 0],
            name="Overlap network",
            type="O",
        )
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz

        self.activate_save_statevector()
        f = np.ones(2**num_ip)
        f = f / np.linalg.norm(f)
        self.function = Statevector(f)
        self.h_n = 2 / (2**num_ip)
        self.rescaling = 1 / 10
        self.energy = None

    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):
            self.assign_parameters(params)

        self.set_measure_index(0)
        state_current = self.simulate_state_vector()
        overlap = np.real(state_current.get_overlap_with_target(self.function))
        self.energy = -self.rescaling * overlap # * np.sqrt(self.h_n)

        op = Operator.create_laplace_pbc(self.IPs[0])
        state_current_f = state_current.get_amplitudes() / np.sqrt(self.h_n)
        # print(op.op.shape, state_current.get_amplitudes().shape,)
        diff = op.op @ state_current.get_amplitudes() - self.rescaling * self.function.get_amplitudes()
        print("DIFF", diff)
        print("LAPLACE X F: ", op.op @ state_current.get_amplitudes(), self.function.get_amplitudes(), self.h_n)
        # print(np.dot(op.op, state_current.get_amplitudes()))
        # print(self.function.get_amplitudes())
        # print("ENERGY OPErator:", self.h_n * state_current.get_amplitudes() @ op.op @ state_current.get_amplitudes())
        # print("ZERO??", op.op @ np.ones(2**self.IPs[0]))
        return self.energy

    def get_nth_gradient_component(self, params, shots, nth):
        dtheta = 1e-5
        params[nth] += dtheta
        pos = self.compute_cost(params)
        params[nth] -= 2*dtheta
        neg = self.compute_cost(params)
        grad = (pos - neg) / (2*dtheta)
        params[nth] += dtheta

        params[nth] += np.pi
        self.assign_parameters(params)
        state_current = self.simulate_state_vector()

        grad2 = 1/2 * np.real(state_current.get_overlap_with_target(self.function)) * np.sqrt(self.h_n)
        grad2 = -grad2 * self.rescaling
        params[nth] -= np.pi
        # print(grad, grad2)
        # raise ValueError
        return grad2

    @classmethod
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs):
        # if g == 0:
        #    return NonLinearNetwork(qc=QuantumCircuit(1), registers=[], num_ip=1, g=g)

        qc, registers = OverlapNetwork.initialise_registers_fake(num_ip)
        if ansatz:
            ansatz = deepcopy(ansatz)
            gate = ansatz.qc.to_gate(label="Ansatz")
            qc.compose(gate, inplace=True)

        # if hadamard:
        #     qc.h(registers[0][0])

        # NonLinearNetwork.build_gate
        # qc.compose(nonlinear gate, qubits=range(0, 2 * num_ip + 1), inplace=True)
        # if hadamard:
        #     qc.h(registers[0][0])
        return OverlapNetwork(
            qc, registers, num_ip, ansatz=ansatz
        )

    @staticmethod
    def initialise_registers_fake(num_ip):
        ip1 = QuantumRegister(num_ip, "IP1")
        registers = [ip1]
        qc = QuantumCircuit(*registers)
        qc.barrier()
        return qc, [ip1]


class Laplace(Network):
    def __init__(self, qc, registers, num_ip, ansatz=None):
        super().__init__(
            qc,
            registers,
            [num_ip],
            [1, num_ip - 2],
            name="Laplace Network",
            type="L",
        )
        if ansatz:
            self.pmv_dict = ansatz.pmv_dict
            self.ansatz = ansatz
        self.h_n = 2 / (2**num_ip)
        self.rescaling = 2 / (self.h_n**2)
        self.energy = None

    # def prepend_ansatz_state(self, params):
    #     print(params)
    #     state = self.ansatz.get_prepared_state(params)
    #     print(self.qc)
    #     self.qc_assigned, registers = initialise_registers_kinetic(self.IPs[0])
    #     gate = StatePreparation(state.get_amplitudes(), label="Ansatz")
    #     a = self.qc_assigned.compose(gate, qubits=[3, 4, 5, 6])
    #     print(a)

    def compute_cost(self, params: np.ndarray = None, shots: int = 1024):
        if isinstance(params, (np.ndarray, list)):
            self.assign_parameters(params)

        self.set_measure_index(0)
        sigma = self.estimate_sigma(shots)
        self.energy = (-2 + 2 * sigma) / (self.h_n**2)

        state = self.ansatz.get_state_vector(params)
        op = Operator.create_laplace_pbc(self.IPs[0])
        energy = state.get_amplitudes() @ op.op @ state.get_amplitudes()
        print("ENERGY LAPLACE", self.energy, energy)
        return self.energy

    def get_nth_gradient_component(self, params, shots, nth):
        params[nth] += np.pi / 2
        self.assign_parameters(params)
        sigma_ps = self.estimate_sigma(shots)
        self.increment_measure_index()

        params[nth] -= np.pi
        self.assign_parameters(params)
        sigma_ns = self.estimate_sigma(shots)
        self.increment_measure_index()

        res = self.rescaling * (sigma_ps - sigma_ns) * 1 / 2
        params[nth] += np.pi / 2
        return res

    @classmethod
    def construct(cls, num_ip, hadamard=True, ansatz=None, **kwargs):
        qc, registers = KineticNetwork.initialise_registers(num_ip, anc_reset=True)
        if ansatz:
            ansatz = deepcopy(ansatz)
            gate = ansatz.qc.to_gate(label="Ansatz")
            qc.compose(gate, qubits=range(num_ip - 1, 2 * num_ip - 1), inplace=True)
            qc.barrier()

        if hadamard:
            qc.h(registers[0][0])
        adder_gate = KineticNetwork.build_adder_gate(num_ip, control=1)
        qc.compose(adder_gate, qubits=range(0, 2 * num_ip - 1), inplace=True)
        if hadamard:
            qc.h(registers[0][0])
        return Laplace(qc, registers, num_ip, ansatz=ansatz)

    @staticmethod
    def initialise_registers(num_ip, anc_reset: bool = True):
        """
        This function initiales a quantum circuit with kinetic type structure,
        that means n main qubits, n-2 ancilla qubits as part of the QNPU and
        1 outer ancilla qubit
        Args:
            num_ip: Number of qubits of Input Port
            anc_reset: If true, the n-2 ancilla qubit get
                       reset to |0>

        Returns:
            QuantumCircuit, List[Registers]
        """
        ancilla_cp = QuantumRegister(1, "ancCP")
        ancilla = QuantumRegister(num_ip - 2, "ancQNPU")
        ip1 = QuantumRegister(num_ip, "IP1")
        registers = [ancilla_cp, ancilla, ip1]
        qc = QuantumCircuit(*registers)
        qc.barrier()
        if anc_reset:
            for anc in ancilla:
                qc.reset(anc)
            for anc in ancilla_cp:
                qc.reset(anc)
        return qc, [ancilla_cp, ancilla, ip1]


def init_network_kinetic_with_ansatz_su2(ip_number, reps):
    nw = Network.create_ansatz_network_su2(ip_number, term="kin", reps=reps)
    nw_adder = Network.create_kinetic_network(ip_number)
    nw_master = Network.add_networks(nw, nw_adder, name="ansatz_with_kinetic_init")
    return nw_master


def init_network_identity(ip_number, reps):
    nw = Network.create_ansatz_network_su2(ip_number, term="kin", reps=reps)
    nw_adder = Network.create_kinetic_network(ip_number, control_active=False)
    nw_master = Network.add_networks(nw, nw_adder, name="ansatz_with_identity")
    return nw_master


def get_network(term):
    if term.upper() == "K":
        return KineticNetwork
    elif term.upper() == "V":
        return PotentialNetwork
    elif term.upper() == "I":
        return NonLinearNetwork
    elif term.upper() == "O":
        return OverlapNetwork
    elif term.upper() == "L":
        return Laplace
    else:
        raise Exception(f"""No network for term '{term}' known!""")


主函数main_dynamic_2.py 如下：

 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import scipy
from scipy.optimize import minimize
from qiskit import QuantumCircuit, Aer, transpile
from qiskit.circuit import ParameterVector
from measurement.dynamic_povm_tomography import perform_single_qubit_dynamic_povm_tomography

# 单量子比特变分电路（2参数：RY -> RZ）
class SingleQubitAnsatz:
    def __init__(self):
        self.qc = QuantumCircuit(1)
        self.num_parameters = 2
        self.param_vector = ParameterVector("theta", self.num_parameters)
        self.qc.ry(self.param_vector[0], 0)
        self.qc.rz(self.param_vector[1], 0)

    def assign_parameters(self, params):
        assign_dict = {self.param_vector[i]: params[i] for i in range(self.num_parameters)}
        return self.qc.assign_parameters(assign_dict)

# 保真度定义：相对于 |1> 态的保真度
def fidelity_with_1(rho):
    return np.real(rho[1, 1])

# 成本函数：cost = 1 - fidelity
class VQACost:
    def __init__(self, ansatz):
        self.ansatz = ansatz
        self.delta = 1e-5

    def cost(self, params):
        qc_assigned = self.ansatz.assign_parameters(params)
        rho_est = perform_single_qubit_dynamic_povm_tomography(qc_assigned, shots=8192)
        fid = fidelity_with_1(rho_est)
        return 1.0 - fid

    def cost_and_grad(self, params):
        cval = self.cost(params)
        grad = np.zeros_like(params)
        for i in range(len(params)):
            plus = np.copy(params)
            plus[i] += self.delta
            c_plus = self.cost(plus)
            minus = np.copy(params)
            minus[i] -= self.delta
            c_minus = self.cost(minus)
            grad[i] = (c_plus - c_minus) / (2 * self.delta)
        return cval, grad

# 主函数：优化变分参数以达到高保真度
def main():
    ans = SingleQubitAnsatz()
    cost_manager = VQACost(ans)

    print("变分电路（符号形式）:")
    print(ans.qc)

    N_RESTARTS = 10
    best_fid = 0.0
    best_params = None

    for attempt in range(1, N_RESTARTS + 1):
        init_params = np.random.rand(ans.num_parameters) * 2 * np.pi
        print(f"\n=== 第 {attempt} 次尝试，初始参数={init_params} ===")
        print("运行 L-BFGS-B 优化")

        def fun_and_jac(x):
            val, grad = cost_manager.cost_and_grad(x)
            return val, grad

        res = minimize(
            fun_and_jac,
            init_params,
            jac=True,
            method='L-BFGS-B',
            options={'maxiter': 300, 'gtol': 1e-14, 'ftol': 1e-14, 'disp': True}
        )
        final_cost = res.fun
        final_fid = 1.0 - final_cost
        print(f"第 {attempt} 次尝试结束: cost={final_cost:.6f}, fidelity={final_fid:.6f}")

        if final_fid > best_fid:
            best_fid = final_fid
            best_params = res.x

        if final_fid >= 0.95:
            print(f"保真度达到 >= 0.95，跳过剩余尝试。")
            break

    print("\n============================")
    print("所有尝试完成！")
    print(f"最佳保真度= {best_fid:.6f}")

    if best_params is not None:
        qc_final = ans.assign_parameters(best_params)
        print("最佳参数:", best_params)
        print("\n最终电路:")
        print(qc_final)

        rho_final = perform_single_qubit_dynamic_povm_tomography(qc_final, shots=8192)
        print("\n重构的密度矩阵:")
        print(rho_final)
        print("对角元素=", np.diag(rho_final))
        print(f"\n最终保真度= {fidelity_with_1(rho_final):.6f}")

        # 验证真实状态
        sim = Aer.get_backend("statevector_simulator")
        t_circ = transpile(qc_final, sim)
        result = sim.run(t_circ).result()
        psi = result.get_statevector(t_circ)
        rho_true = np.outer(psi, psi.conj())
        print("\n真实密度矩阵（状态矢量计算）:")
        print(rho_true)
        print("真实保真度= ", fidelity_with_1(rho_true))
    else:
        print("未找到解，这在单量子比特理想情况下不应该发生！")

if __name__ == "__main__":
    main()

你先深度思考和认真理解这些代码。之后我告诉你我的需求。