
# ansatz.py

import copy
import numpy as np
from qiskit import QuantumCircuit, QuantumRegister
from qiskit import Aer
simulator = Aer.get_backend("aer_simulator")
from qiskit.circuit import ParameterVector


class SU2:
    """
    变分量子电路的示例：单比特或多比特的交替 RY/RZ + CNOT。
    若 ip_number=1，则自动跳过 CNOT。
    """

    def __init__(
        self,
        ip_number,
        reps,
        layer_scheme="YC",
        include_barriers=True,
        entanglement="circular",
    ):
        self.registers = [QuantumRegister(ip_number)]
        self.ip_number = ip_number
        self.reps = reps
        self.scheme = layer_scheme
        self.include_barriers = include_barriers
        self.entanglement = entanglement

        self.qc = QuantumCircuit(*self.registers)
        # 预构造一些“自然梯度”块(可能在VQA中用到)，本示例不深入使用
        self.natgrad_blocks = self.construct_natgrad_blocks_2(ip_number, reps)
        # 正式构造 ansatz
        self.num_parameters, self.param_vector = self.setup_ansatz()
        self.num_layers = (len(layer_scheme) - 1) * (1 + reps)

    def setup_ansatz(self):
        # 计算参数数量：每个“非C”层对 ip_number 个量子比特都加旋转门
        num_parameters = (len(self.scheme) - 1) * self.ip_number * (self.reps + 1)
        layers_str = (self.scheme * (self.reps + 1))[:-1]
        param_vector = ParameterVector("SU2", num_parameters)

        count = 0
        for i, layer in enumerate(layers_str):
            if layer == "C":
                SU2.append_cnot_layer(self.qc, self.ip_number, self.entanglement)
                if self.include_barriers:
                    self.qc.barrier()
            else:
                params = param_vector[self.ip_number * count : self.ip_number * (count + 1)]
                SU2.append_r_layer(self.qc, params, layer)
                count += 1

        return num_parameters, param_vector

    def construct_natgrad_blocks_2(self, ip_number, reps):
        # 与上面 setup_ansatz 类似，只是细节略有不同，这里直接保留原逻辑
        layers_str = ("YC" * (reps + 1))[:-1]
        result = []
        qc = copy.deepcopy(self.qc)
        for i in range(1, len(layers_str) + 1):
            count = 0
            block_str = layers_str[:i]
            if block_str[-1] == "C":
                pass
            else:
                qc = copy.deepcopy(self.qc)
                num_rotation_layers = len(block_str.replace("C", "")) - 1
                param_vector = ParameterVector(
                    f"SU2_block", num_rotation_layers * ip_number
                )
                for j, layer in enumerate(block_str):
                    if layer == "C":
                        SU2.append_cnot_layer(qc, ip_number, self.entanglement)
                        qc.barrier()
                    else:
                        params = param_vector[
                            ip_number * count : ip_number * (count + 1)
                        ]
                        SU2.append_r_layer(qc, params, layer)
                        count += 1
                if block_str[-1] == "Y":
                    SU2.rotate_psi(qc, ip_number)
                result.append(qc)
        return result

    @staticmethod
    def append_r_layer(qc, params, layer):
        for i, param in enumerate(params):
            if layer == "Y":
                qc.ry(param, i)
            elif layer == "Z":
                qc.rz(param, i)
            else:
                raise ValueError("Layer not a rotation Y-/Z-Gate")

    @staticmethod
    def append_cnot_layer(qc, ip_number, entanglement):
        # 如果只有单比特，则跳过 CNOT
        if ip_number < 2:
            return

        cnot_circular = True
        if entanglement == "circular":
            if cnot_circular:
                qc.cnot(ip_number - 1, 0)
            for i in range(ip_number - 1):
                qc.cnot(i, (i + 1))
        elif entanglement == "full":
            for i in range(ip_number - 1):
                for j in range(i + 1, ip_number):
                    qc.cnot(i, j)
        elif entanglement == "rev":
            for i in range(ip_number - 2, 0):
                qc.cnot(i, i + 1)

    @staticmethod
    def rotate_psi(qc, ip_number):
        for i in range(ip_number):
            qc.sdg(i)
            qc.h(i)
