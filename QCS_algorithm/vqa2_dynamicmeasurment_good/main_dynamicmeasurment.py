
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import scipy
from scipy.optimize import minimize
from qiskit import QuantumCircuit, Aer, transpile
from qiskit.circuit import ParameterVector
from measurement.dynamic_povm_tomography import perform_single_qubit_dynamic_povm_tomography

# 单量子比特变分电路（2参数：RY -> RZ）
class SingleQubitAnsatz:
    def __init__(self):
        self.qc = QuantumCircuit(1)
        self.num_parameters = 2
        self.param_vector = ParameterVector("theta", self.num_parameters)
        self.qc.ry(self.param_vector[0], 0)
        self.qc.rz(self.param_vector[1], 0)

    def assign_parameters(self, params):
        assign_dict = {self.param_vector[i]: params[i] for i in range(self.num_parameters)}
        return self.qc.assign_parameters(assign_dict)

# 保真度定义：相对于 |1> 态的保真度
def fidelity_with_1(rho):
    return np.real(rho[1, 1])

# 成本函数：cost = 1 - fidelity
class VQACost:
    def __init__(self, ansatz):
        self.ansatz = ansatz
        self.delta = 1e-5

    def cost(self, params):
        qc_assigned = self.ansatz.assign_parameters(params)
        rho_est = perform_single_qubit_dynamic_povm_tomography(qc_assigned, shots=8192)
        fid = fidelity_with_1(rho_est)
        return 1.0 - fid

    def cost_and_grad(self, params):
        cval = self.cost(params)
        grad = np.zeros_like(params)
        for i in range(len(params)):
            plus = np.copy(params)
            plus[i] += self.delta
            c_plus = self.cost(plus)
            minus = np.copy(params)
            minus[i] -= self.delta
            c_minus = self.cost(minus)
            grad[i] = (c_plus - c_minus) / (2 * self.delta)
        return cval, grad

# 主函数：优化变分参数以达到高保真度
def main():
    ans = SingleQubitAnsatz()
    cost_manager = VQACost(ans)

    print("变分电路（符号形式）:")
    print(ans.qc)

    N_RESTARTS = 10
    best_fid = 0.0
    best_params = None

    for attempt in range(1, N_RESTARTS + 1):
        init_params = np.random.rand(ans.num_parameters) * 2 * np.pi
        print(f"\n=== 第 {attempt} 次尝试，初始参数={init_params} ===")
        print("运行 L-BFGS-B 优化")

        def fun_and_jac(x):
            val, grad = cost_manager.cost_and_grad(x)
            return val, grad

        res = minimize(
            fun_and_jac,
            init_params,
            jac=True,
            method='L-BFGS-B',
            options={'maxiter': 300, 'gtol': 1e-14, 'ftol': 1e-14, 'disp': True}
        )
        final_cost = res.fun
        final_fid = 1.0 - final_cost
        print(f"第 {attempt} 次尝试结束: cost={final_cost:.6f}, fidelity={final_fid:.6f}")

        if final_fid > best_fid:
            best_fid = final_fid
            best_params = res.x

        if final_fid >= 0.95:
            print(f"保真度达到 >= 0.95，跳过剩余尝试。")
            break

    print("\n============================")
    print("所有尝试完成！")
    print(f"最佳保真度= {best_fid:.6f}")

    if best_params is not None:
        qc_final = ans.assign_parameters(best_params)
        print("最佳参数:", best_params)
        print("\n最终电路:")
        print(qc_final)

        rho_final = perform_single_qubit_dynamic_povm_tomography(qc_final, shots=8192)
        print("\n重构的密度矩阵:")
        print(rho_final)
        print("对角元素=", np.diag(rho_final))
        print(f"\n最终保真度= {fidelity_with_1(rho_final):.6f}")

        # 验证真实状态
        sim = Aer.get_backend("statevector_simulator")
        t_circ = transpile(qc_final, sim)
        result = sim.run(t_circ).result()
        psi = result.get_statevector(t_circ)
        rho_true = np.outer(psi, psi.conj())
        print("\n真实密度矩阵（状态矢量计算）:")
        print(rho_true)
        print("真实保真度= ", fidelity_with_1(rho_true))
    else:
        print("未找到解，这在单量子比特理想情况下不应该发生！")

if __name__ == "__main__":
    main()