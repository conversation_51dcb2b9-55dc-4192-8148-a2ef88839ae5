import h5py
import os
import pathlib


from mpi4py import MPI
import numpy as np

comm = MPI.COMM_WORLD
rank = comm.Get_rank()
h5py.get_config().track_order = True


# TODO Rename to HDF5Handler
# TODO Path does not have to bne complicated, just sth.hdf5 - not all attributes needed
# TODO Class method which returns HDF5 Handler from hdf5 file
class HDF5Handler:
    def __init__(self, mode="w", dir="dump", index=1, parallel=False):
        self.parallel = parallel
        self.f, self.filename = self.open_or_create_file(mode, dir, index)
        self.main_grp = self.f.create_group("Main group")
        self.sim_grp = self.main_grp.create_group("Simulation")
        self.state_grp = self.main_grp.create_group("Final state")
        self.it_offset = 0
        self.f.attrs["#MPI cores"] = comm.size

    def set_attributes(self, attrs):
        for key, val in attrs.items():
            if isinstance(val, dict):
                val = str(val)
            self.f.attrs[key] = val

    def save_iteration(self, cost, true_cost, gradient, params, anc_densities, iteration):
        self.it_offset = iteration
        iter_grp = self.sim_grp.create_group(f"{iteration}")
        iter_grp.create_dataset("Cost", data=cost, dtype="float64")
        iter_grp.create_dataset("True Cost", data=true_cost, dtype="float64")
        iter_grp.create_dataset("Parameters", data=params, dtype="float64")
        iter_grp.create_dataset("Gradient", data=gradient, dtype="float64")
        for term, rho in anc_densities:
            iter_grp.create_dataset(f"{term}_rho_real", data=np.real(rho), dtype="float64")
            iter_grp.create_dataset(f"{term}_rho_imag", data=np.imag(rho), dtype="float64")
        # cost_dset[()] = cost
        # true_cost_dset[()] = true_cost
        # params_dset[:] = params

    def save_final_state(self, state):
        self.state_grp.create_dataset(
            "final_state_real", data=state.get_real_part(), dtype="f"
        )
        self.state_grp.create_dataset(
            "final_state_imag", data=state.get_imag_part(), dtype="f"
        )

    def open_or_create_file(self, mode, dir, index):
        os.makedirs(name=dir, exist_ok=True)
        filepath = f"{dir}/output_{index}.hdf5"
        print("HDF5Handler: Filepath: ", filepath, flush=True)
        if self.parallel:
            f = h5py.File(filepath, mode, driver="mpio", comm=comm)
        else:
            f = h5py.File(filepath, mode)
        return f, filepath

    @staticmethod
    def generate_path_from_attrs_depr(attrs):
        attrs_str_general = "_".join(
            [f"{tup[0]}_{tup[1]}" for tup in attrs["General"].items()]
        )
        attrs_str_opt = "_".join(
            [f"{tup[0]}_{tup[1]}" for tup in attrs["Optimization"].items()]
        )
        attrs_str = "_".join([attrs_str_general, attrs_str_opt])
        dump_folder = pathlib.PurePath(os.getcwd(), "dump")
        if not os.path.exists(dump_folder):
            os.makedirs(dump_folder)
        path = pathlib.PurePath(dump_folder, attrs_str + ".hdf5")
        return path
